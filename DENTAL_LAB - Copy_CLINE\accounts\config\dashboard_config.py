"""
Dashboard Configuration
Centralized settings for dashboard behavior and appearance
"""

from django.conf import settings
from typing import Dict, List, Any

class DashboardConfig:
    """Dashboard configuration class"""
    
    # Cache settings
    CACHE_TIMEOUT = getattr(settings, 'DASHBOARD_CACHE_TIMEOUT', 300)  # 5 minutes
    
    # Pagination settings
    DEFAULT_PAGE_SIZE = 10
    MAX_PAGE_SIZE = 100
    
    # Chart settings
    CHART_COLORS = [
        '#4285F4',  # Primary Blue
        '#34A853',  # Success Green  
        '#EA4335',  # Danger Red
        '#FBBC05',  # Warning Yellow
        '#46bdc6',  # Info Cyan
        '#a142f4',  # Purple
        '#ff6d01',  # Orange
        '#0d7377'   # Teal
    ]
    
    # Date range options (in days)
    DATE_RANGE_OPTIONS = [7, 30, 90, 180, 365, 730]
    DEFAULT_DATE_RANGE = 30
    
    # Metrics refresh intervals (in seconds)
    METRICS_REFRESH_INTERVAL = 60  # 1 minute
    CHARTS_REFRESH_INTERVAL = 300  # 5 minutes
    
    # Dashboard layout settings
    METRICS_GRID_COLUMNS = {
        'xs': 1,  # Mobile
        'sm': 2,  # Tablet  
        'md': 3,  # Small desktop
        'lg': 6,  # Large desktop
    }
    
    # Feature flags
    FEATURES = {
        'real_time_updates': getattr(settings, 'DASHBOARD_REAL_TIME', True),
        'dark_mode': getattr(settings, 'DASHBOARD_DARK_MODE', True),
        'export_data': getattr(settings, 'DASHBOARD_EXPORT', True),
        'notifications': getattr(settings, 'DASHBOARD_NOTIFICATIONS', True),
        'caching': getattr(settings, 'DASHBOARD_CACHING', True),
    }
    
    # Performance settings
    MAX_RECENT_CASES = 8
    MAX_UPCOMING_DEADLINES = 6
    MAX_TOP_PERFORMERS = 5
    MAX_DEPARTMENTS = 5
    
    @classmethod
    def get_date_range_options(cls) -> List[Dict[str, Any]]:
        """Get formatted date range options for select element"""
        options = [
            {'value': 7, 'label': 'Last 7 Days'},
            {'value': 30, 'label': 'Last 30 Days'},
            {'value': 90, 'label': 'Last 3 Months'},
            {'value': 180, 'label': 'Last 6 Months'},
            {'value': 365, 'label': 'Last 12 Months'},
            {'value': 730, 'label': 'Last 24 Months'},
        ]
        return options
    
    @classmethod
    def validate_date_range(cls, days: int) -> int:
        """Validate and return a valid date range"""
        if days in cls.DATE_RANGE_OPTIONS:
            return days
        return cls.DEFAULT_DATE_RANGE


# Status color mapping for consistent UI
STATUS_COLORS = {
    'pending_acceptance': 'var(--warning)',
    'in_progress': 'var(--info)',
    'on_hold': 'var(--gray)',
    'quality_check': 'var(--purple)',
    'revision_needed': 'var(--danger)',
    'ready_to_ship': 'var(--purple)',
    'shipped': 'var(--success)',
    'delivered': 'var(--success)',
    'completed': 'var(--success)',
    'cancelled': 'var(--gray)',
}

# Priority color mapping
PRIORITY_COLORS = {
    1: 'var(--danger)',    # High
    2: 'var(--primary)',   # Normal  
    3: 'var(--success)',   # Low
}
