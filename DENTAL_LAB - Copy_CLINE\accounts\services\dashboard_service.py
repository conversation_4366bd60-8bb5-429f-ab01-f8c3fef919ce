"""
Dashboard Service Layer
Separates data aggregation logic from view presentation
"""

from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta
import json
import logging

from django.utils import timezone
from django.db.models import Count, Sum, Avg, Q, Max, F
from django.db.models.functions import TruncDay, TruncMonth
from django.core.cache import cache

from case.models import Case, Department, Task
from Dentists.models import Dentist
from billing.models import Invoice
from finance.models import Payment
from patients.models import Patient

logger = logging.getLogger(__name__)


class DashboardDataService:
    """Service class for aggregating dashboard data"""
    
    def __init__(self, date_range_days: int = 30):
        self.date_range_days = date_range_days
        self.today = timezone.now().date()
        self._setup_date_ranges()
    
    def _setup_date_ranges(self):
        """Setup commonly used date ranges"""
        self.start_of_week = self.today - timedelta(days=self.today.weekday())
        self.start_of_month = self.today.replace(day=1)
        self.start_of_last_month = (self.start_of_month - timedelta(days=1)).replace(day=1)
        self.range_start_date = self.today - timedelta(days=self.date_range_days)
        self.thirty_days_ago = self.today - timedelta(days=30)
        
        # Check if we have recent data and adjust accordingly
        self._adjust_for_data_availability()
    
    def _adjust_for_data_availability(self):
        """Adjust date ranges based on actual data availability"""
        has_recent_data = Case.objects.filter(
            received_date_time__date__gte=self.today - timedelta(days=30)
        ).exists()
        
        if not has_recent_data:
            latest_case_date = Case.objects.filter(
                received_date_time__isnull=False
            ).aggregate(latest=Max('received_date_time'))['latest']
            
            if latest_case_date:
                self.today = latest_case_date.date()
                logger.info(f"Adjusted dashboard reference date to {self.today}")
                self._setup_date_ranges()  # Recalculate with new today
    
    def get_basic_metrics(self) -> Dict[str, Any]:
        """Get basic case counting metrics"""
        cache_key = f"dashboard_basic_metrics_{self.today}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        total_cases = Case.objects.count()
        cases_today = Case.objects.filter(received_date_time__date=self.today).count()
        cases_this_week = Case.objects.filter(
            received_date_time__date__gte=self.start_of_week
        ).count()
        cases_this_month = Case.objects.filter(
            received_date_time__date__gte=self.start_of_month
        ).count()
        cases_last_month = Case.objects.filter(
            received_date_time__date__gte=self.start_of_last_month,
            received_date_time__date__lt=self.start_of_month
        ).count()
        
        # Calculate growth
        month_growth = 0
        if cases_last_month > 0:
            month_growth = ((cases_this_month - cases_last_month) / cases_last_month) * 100
        
        metrics = {
            'total_cases': total_cases,
            'cases_today': cases_today,
            'cases_this_week': cases_this_week,
            'cases_this_month': cases_this_month,
            'cases_last_month': cases_last_month,
            'month_growth': round(month_growth, 1),
        }
        
        cache.set(cache_key, metrics, 300)  # Cache for 5 minutes
        return metrics
    
    def get_status_metrics(self) -> Dict[str, Any]:
        """Get case status breakdown metrics"""
        cache_key = f"dashboard_status_metrics_{self.today}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        status_counts = Case.objects.values('status').annotate(
            count=Count('case_number')
        ).order_by('-count')
        
        overdue_cases = Case.objects.filter(
            deadline__lt=self.today,
            status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
        ).count()
        
        ready_to_ship = Case.objects.filter(status='ready_to_ship').count()
        in_progress = Case.objects.filter(status='in_progress').count()
        
        # Calculate on-time completion rate
        completed_cases = Case.objects.filter(
            status__in=['completed', 'delivered'],
            received_date_time__date__gte=self.range_start_date
        )
        on_time_cases = completed_cases.filter(
            actual_completion__lte=F('deadline')
        ).count() if completed_cases.exists() else 0
        
        total_completed = completed_cases.count()
        on_time_completion_rate = (on_time_cases / total_completed * 100) if total_completed > 0 else 0
        
        metrics = {
            'status_counts': list(status_counts),
            'overdue_cases': overdue_cases,
            'ready_to_ship': ready_to_ship,
            'in_progress': in_progress,
            'on_time_completion_rate': round(on_time_completion_rate, 1),
            'ready_to_ship_count': ready_to_ship,  # For template compatibility
        }
        
        cache.set(cache_key, metrics, 300)
        return metrics
    
    def get_financial_metrics(self) -> Dict[str, Any]:
        """Get financial performance metrics"""
        cache_key = f"dashboard_financial_metrics_{self.start_of_month}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            total_invoiced_month = Invoice.objects.filter(
                date__gte=self.start_of_month
            ).aggregate(total=Sum('total_amount'))['total'] or 0
            
            total_paid_month = Payment.objects.filter(
                date__gte=self.start_of_month
            ).aggregate(total=Sum('amount'))['total'] or 0
            
            unpaid_invoices = Invoice.objects.filter(
                status__in=['unpaid', 'partial']
            ).count()
            
            # Calculate collection rate
            collection_rate = (total_paid_month / total_invoiced_month * 100) if total_invoiced_month > 0 else 0
            
        except Exception as e:
            logger.error(f"Error calculating financial metrics: {e}")
            total_invoiced_month = 0
            total_paid_month = 0
            unpaid_invoices = 0
            collection_rate = 0
        
        metrics = {
            'total_invoiced_month': float(total_invoiced_month),
            'total_paid_month': float(total_paid_month),
            'unpaid_invoices': unpaid_invoices,
            'collection_rate': round(collection_rate, 1),
        }
        
        cache.set(cache_key, metrics, 300)
        return metrics
    
    def get_recent_data(self) -> Dict[str, Any]:
        """Get recent cases and upcoming deadlines"""
        recent_cases = Case.objects.select_related('dentist', 'patient').filter(
            received_date_time__date__gte=self.range_start_date
        ).order_by('-received_date_time')[:8]
        
        upcoming_deadlines = Case.objects.filter(
            deadline__gte=self.today,
            deadline__lte=self.today + timedelta(days=7),
            status__in=['pending_acceptance', 'in_progress', 'on_hold']
        ).order_by('deadline')[:6]
        
        return {
            'recent_cases': recent_cases,
            'upcoming_deadlines': upcoming_deadlines,
        }
    
    def get_performance_data(self) -> Dict[str, Any]:
        """Get top performers and department workload"""
        top_dentists = Dentist.objects.annotate(
            case_count=Count('dentist_cases', 
                           filter=Q(dentist_cases__received_date_time__date__gte=self.start_of_month))
        ).order_by('-case_count')[:5]
        
        try:
            departments = Department.objects.annotate(
                active_tasks=Count('workflow_stages__tasks',
                                 filter=Q(workflow_stages__tasks__status__in=['pending', 'in_progress'])),
                total_tasks=Count('workflow_stages__tasks')
            ).order_by('-active_tasks')[:5]
        except Exception as e:
            logger.error(f"Error getting department data: {e}")
            departments = []
        
        return {
            'top_dentists': top_dentists,
            'departments': departments,
        }


class TrendDataService:
    """Service for generating trend data and charts"""
    
    def __init__(self, reference_date: date = None):
        self.today = reference_date or timezone.now().date()
    
    def get_trend_data(self, period: str, range_days: int = None) -> Dict[str, Any]:
        """Get trend data for specified period"""
        if period == "30_days":
            return self._get_daily_trend(range_days or 30)
        elif period == "12_months":
            return self._get_monthly_trend(12)
        elif period == "24_months":
            return self._get_monthly_trend(24)
        elif period == "all_time":
            return self._get_all_time_trend()
        else:
            raise ValueError(f"Invalid period: {period}")
    
    def _get_daily_trend(self, days: int) -> Dict[str, Any]:
        """Get daily trend data for specified number of days"""
        start_date = self.today - timedelta(days=days)
        
        case_trends = Case.objects.filter(
            received_date_time__date__gte=start_date
        ).annotate(
            date=TruncDay('received_date_time')
        ).values('date').annotate(
            count=Count('case_number')
        ).order_by('date')
        
        # Create lookup dict
        trend_data = {item['date'].isoformat(): item['count'] 
                     for item in case_trends if item['date']}
        
        # Fill missing dates
        trends = []
        for i in range(days, -1, -1):
            date = self.today - timedelta(days=i)
            trends.append({
                'date': date.isoformat(),
                'count': trend_data.get(date.isoformat(), 0)
            })
        
        return {
            'data': trends,
            'json': json.dumps(trends),
        }
    
    def _get_monthly_trend(self, months: int) -> Dict[str, Any]:
        """Get monthly trend data for specified number of months"""
        start_date = self.today - relativedelta(months=months)
        
        case_trends = Case.objects.filter(
            received_date_time__date__gte=start_date
        ).annotate(
            month=TruncMonth('received_date_time')
        ).values('month').annotate(
            count=Count('case_number')
        ).order_by('month')
        
        # Create lookup dict
        trend_data = {item['month'].isoformat()[:7]: item['count'] 
                     for item in case_trends if item['month']}
        
        # Fill missing months
        trends = []
        for i in range(months, -1, -1):
            date = self.today - relativedelta(months=i)
            month_str = date.isoformat()[:7]
            first_day = date.replace(day=1).isoformat()
            trends.append({
                'month': first_day,
                'count': trend_data.get(month_str, 0)
            })
        
        return {
            'data': trends,
            'json': json.dumps(trends),
        }
    
    def _get_all_time_trend(self) -> Dict[str, Any]:
        """Get all-time monthly trend data"""
        first_case = Case.objects.order_by('received_date_time').first()
        start_date = first_case.received_date_time.date() if first_case else self.today - timedelta(days=365)
        
        months_diff = ((self.today.year - start_date.year) * 12 + 
                      self.today.month - start_date.month) + 1
        
        if months_diff < 24:
            months_diff = 24
            start_date = self.today - relativedelta(months=months_diff)
        
        return self._get_monthly_trend(months_diff)


class ChartDataService:
    """Service for preparing chart-specific data"""
    
    def get_status_chart_data(self) -> List[Dict[str, Any]]:
        """Get data formatted for status chart"""
        status_counts = Case.objects.values('status').annotate(
            count=Count('case_number')
        ).order_by('-count')
        
        return [{'status': item['status'], 'total': item['count']} 
                for item in status_counts]
    
    def get_priority_chart_data(self) -> List[Dict[str, Any]]:
        """Get data formatted for priority chart"""
        priority_counts = Case.objects.values('priority').annotate(
            total=Count('case_number')
        ).order_by('priority')
        
        return list(priority_counts)
    
    def get_department_chart_data(self) -> List[Dict[str, Any]]:
        """Get data formatted for department chart"""
        department_counts = Case.objects.values('responsible_department__name').annotate(
            total_cases=Count('case_number')
        ).order_by('-total_cases')
        
        return list(department_counts)


class DashboardService:
    """Main dashboard service orchestrating all data services"""
    
    def __init__(self, date_range_days: int = 30):
        self.date_range_days = date_range_days
        self.data_service = DashboardDataService(date_range_days)
        self.trend_service = TrendDataService(self.data_service.today)
        self.chart_service = ChartDataService()
    
    def get_complete_dashboard_data(self) -> Dict[str, Any]:
        """Get all dashboard data in one consolidated call"""
        try:
            # Get all data sections
            basic_metrics = self.data_service.get_basic_metrics()
            status_metrics = self.data_service.get_status_metrics()
            financial_metrics = self.data_service.get_financial_metrics()
            recent_data = self.data_service.get_recent_data()
            performance_data = self.data_service.get_performance_data()
            
            # Get trend data for all periods
            trend_30_days = self.trend_service.get_trend_data("30_days", self.date_range_days)
            trend_12_months = self.trend_service.get_trend_data("12_months")
            trend_24_months = self.trend_service.get_trend_data("24_months")
            trend_all_time = self.trend_service.get_trend_data("all_time")
            
            # Get chart data
            status_chart = self.chart_service.get_status_chart_data()
            priority_chart = self.chart_service.get_priority_chart_data()
            department_chart = self.chart_service.get_department_chart_data()
            
            # Consolidate all data
            return {
                # Basic metrics
                **basic_metrics,
                **status_metrics,
                **financial_metrics,
                **recent_data,
                **performance_data,
                
                # Trend data
                'trend_30_days_data': trend_30_days['data'],
                'trend_12_months_data': trend_12_months['data'],
                'trend_24_months_data': trend_24_months['data'],
                'trend_all_time_data': trend_all_time['data'],
                
                # JSON for JavaScript
                'trend_30_days_json': trend_30_days['json'],
                'trend_12_months_json': trend_12_months['json'],
                'trend_24_months_json': trend_24_months['json'],
                'trend_all_time_json': trend_all_time['json'],
                
                # Chart data
                'status_chart_data': status_chart,
                'priority_chart_data': priority_chart,
                'department_chart_data': department_chart,
                'status_json': json.dumps([
                    {'status': item['status'], 'count': item['total']}
                    for item in status_chart
                ]),
                
                # Meta data
                'selected_range': str(self.date_range_days),
                'today': self.data_service.today.strftime('%B %d, %Y'),
            }
            
        except Exception as e:
            logger.error(f"Error getting dashboard data: {e}")
            return self._get_fallback_data()
    
    def _get_fallback_data(self) -> Dict[str, Any]:
        """Return minimal data structure when errors occur"""
        return {
            'total_cases': 0,
            'cases_today': 0,
            'cases_this_week': 0,
            'cases_this_month': 0,
            'month_growth': 0,
            'overdue_cases': 0,
            'ready_to_ship': 0,
            'in_progress': 0,
            'on_time_completion_rate': 0,
            'total_invoiced_month': 0,
            'total_paid_month': 0,
            'unpaid_invoices': 0,
            'collection_rate': 0,
            'recent_cases': [],
            'upcoming_deadlines': [],
            'top_dentists': [],
            'departments': [],
            'trend_30_days_data': [],
            'trend_12_months_data': [],
            'trend_24_months_data': [],
            'trend_all_time_data': [],
            'trend_30_days_json': '[]',
            'trend_12_months_json': '[]',
            'trend_24_months_json': '[]',
            'trend_all_time_json': '[]',
            'status_chart_data': [],
            'priority_chart_data': [],
            'department_chart_data': [],
            'status_json': '[]',
            'selected_range': str(self.date_range_days),
            'today': timezone.now().date().strftime('%B %d, %Y'),
            'error': 'Unable to load dashboard data',
        }
