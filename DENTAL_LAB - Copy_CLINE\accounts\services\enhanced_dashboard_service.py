"""
Enhanced Dashboard Service with Financial Metrics
Adds revenue tracking alongside case volume metrics
"""

from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta
import json
import logging
from decimal import Decimal

from django.utils import timezone
from django.db.models import Count, Sum, Avg, Q, Max, F, Case as DBCase, When, DecimalField
from django.db.models.functions import TruncDay, <PERSON>runcMonth, Coalesce
from django.core.cache import cache
from django.conf import settings

from case.models import Case, Department, Task
from Dentists.models import Dentist
from billing.models import Invoice
from finance.models import Payment
from patients.models import Patient

logger = logging.getLogger(__name__)


class FinancialDataService:
    """Service for financial calculations and currency handling"""
    
    def __init__(self, base_currency: str = 'USD'):
        self.base_currency = base_currency
        self.exchange_rates = self._get_exchange_rates()
    
    def _get_exchange_rates(self) -> Dict[str, float]:
        """Get current exchange rates - implement your preferred provider"""
        # Default rates - in production, integrate with live exchange rate API
        return {
            'USD': 1.0,
            'EUR': 0.85,
            'GBP': 0.73,
            'ALL': 100.0,  # Albanian Lek
            'CAD': 1.25,
            'AUD': 1.35,
        }
    
    def convert_currency(self, amount: Decimal, from_currency: str, to_currency: str = None) -> Decimal:
        """Convert amount between currencies"""
        if to_currency is None:
            to_currency = self.base_currency
            
        if from_currency == to_currency:
            return amount
            
        # Convert to base currency first, then to target currency
        usd_amount = amount / Decimal(str(self.exchange_rates.get(from_currency, 1.0)))
        target_amount = usd_amount * Decimal(str(self.exchange_rates.get(to_currency, 1.0)))
        
        return target_amount.quantize(Decimal('0.01'))
    
    def format_currency(self, amount: Decimal, currency: str = None) -> str:
        """Format currency with appropriate symbol and formatting"""
        if currency is None:
            currency = self.base_currency
            
        symbols = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£', 
            'ALL': 'L',
            'CAD': 'C$',
            'AUD': 'A$',
        }
        
        symbol = symbols.get(currency, currency + ' ')
        
        # Format with appropriate decimal places
        if currency == 'ALL':  # Albanian Lek typically doesn't use decimals
            return f"{symbol}{amount:,.0f}"
        else:
            return f"{symbol}{amount:,.2f}"


class EnhancedDashboardDataService:
    """Enhanced service class for aggregating dashboard data with financial metrics"""
    
    def __init__(self, date_range_days: int = 30):
        self.date_range_days = date_range_days
        self.today = timezone.now().date()
        self.financial_service = FinancialDataService()
        self._setup_date_ranges()
    
    def _setup_date_ranges(self):
        """Setup commonly used date ranges"""
        self.start_of_week = self.today - timedelta(days=self.today.weekday())
        self.start_of_month = self.today.replace(day=1)
        self.start_of_last_month = (self.start_of_month - timedelta(days=1)).replace(day=1)
        self.range_start_date = self.today - timedelta(days=self.date_range_days)
        self.thirty_days_ago = self.today - timedelta(days=30)
        
        # Check if we have recent data and adjust accordingly
        self._adjust_for_data_availability()
    
    def _adjust_for_data_availability(self):
        """Adjust date ranges based on actual data availability"""
        has_recent_data = Case.objects.filter(
            received_date_time__date__gte=self.today - timedelta(days=30)
        ).exists()
        
        if not has_recent_data:
            latest_case_date = Case.objects.filter(
                received_date_time__isnull=False
            ).aggregate(latest=Max('received_date_time'))['latest']
            
            if latest_case_date:
                self.today = latest_case_date.date()
                logger.info(f"Adjusted dashboard reference date to {self.today}")
                self._setup_date_ranges()
    
    def _calculate_case_value(self, cases_queryset) -> Decimal:
        """Calculate total value of cases from cost estimates and invoices"""
        total_value = Decimal('0.00')
        
        # Try to get value from cost estimates first
        estimated_value = cases_queryset.aggregate(
            total=Coalesce(Sum('cost_estimate'), Decimal('0.00'))
        )['total'] or Decimal('0.00')
        
        # Try to get actual invoiced amounts
        try:
            case_numbers = list(cases_queryset.values_list('case_number', flat=True))
            invoiced_value = Invoice.objects.filter(
                case_id__in=case_numbers
            ).aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0.00'))
            )['total'] or Decimal('0.00')
            
            # Use invoiced value if available, otherwise use estimates
            total_value = invoiced_value if invoiced_value > 0 else estimated_value
            
        except Exception as e:
            logger.warning(f"Could not calculate invoiced value: {e}")
            total_value = estimated_value
        
        return total_value
    
    def get_enhanced_basic_metrics(self) -> Dict[str, Any]:
        """Get basic case counting metrics enhanced with financial data"""
        cache_key = f"dashboard_enhanced_metrics_{self.today}_{self.date_range_days}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        # Basic case counts
        total_cases = Case.objects.count()
        cases_today = Case.objects.filter(received_date_time__date=self.today)
        cases_this_week = Case.objects.filter(received_date_time__date__gte=self.start_of_week)
        cases_this_month = Case.objects.filter(received_date_time__date__gte=self.start_of_month)
        cases_last_month = Case.objects.filter(
            received_date_time__date__gte=self.start_of_last_month,
            received_date_time__date__lt=self.start_of_month
        )
        cases_range = Case.objects.filter(received_date_time__date__gte=self.range_start_date)
        
        # Calculate financial values
        total_value = self._calculate_case_value(Case.objects.all())
        today_value = self._calculate_case_value(cases_today)
        week_value = self._calculate_case_value(cases_this_week)
        month_value = self._calculate_case_value(cases_this_month)
        last_month_value = self._calculate_case_value(cases_last_month)
        range_value = self._calculate_case_value(cases_range)
        
        # Calculate growth metrics
        month_growth = 0
        month_value_growth = 0
        if cases_last_month.count() > 0:
            month_growth = ((cases_this_month.count() - cases_last_month.count()) / cases_last_month.count()) * 100
        if last_month_value > 0:
            month_value_growth = ((month_value - last_month_value) / last_month_value) * 100
        
        # Calculate average case value
        avg_case_value = total_value / total_cases if total_cases > 0 else Decimal('0.00')
        
        metrics = {
            # Volume metrics
            'total_cases': total_cases,
            'cases_today': cases_today.count(),
            'cases_this_week': cases_this_week.count(),
            'cases_this_month': cases_this_month.count(),
            'cases_last_month': cases_last_month.count(),
            'cases_range': cases_range.count(),
            'month_growth': round(month_growth, 1),
            
            # Financial metrics
            'total_value': total_value,
            'today_value': today_value,
            'week_value': week_value,
            'month_value': month_value,
            'last_month_value': last_month_value,
            'range_value': range_value,
            'month_value_growth': round(month_value_growth, 1),
            'avg_case_value': avg_case_value,
            
            # Formatted currency strings
            'total_value_formatted': self.financial_service.format_currency(total_value),
            'today_value_formatted': self.financial_service.format_currency(today_value),
            'week_value_formatted': self.financial_service.format_currency(week_value),
            'month_value_formatted': self.financial_service.format_currency(month_value),
            'range_value_formatted': self.financial_service.format_currency(range_value),
            'avg_case_value_formatted': self.financial_service.format_currency(avg_case_value),
        }
        
        cache.set(cache_key, metrics, 300)  # Cache for 5 minutes
        return metrics
    
    def get_status_metrics(self) -> Dict[str, Any]:
        """Get case status breakdown metrics with financial data"""
        cache_key = f"dashboard_status_metrics_enhanced_{self.today}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        # Status counts with financial data
        status_data = []
        for status_choice in Case.STATUS_CHOICES:
            status_code = status_choice[0]
            status_label = status_choice[1]
            
            status_cases = Case.objects.filter(status=status_code)
            count = status_cases.count()
            value = self._calculate_case_value(status_cases)
            
            if count > 0:  # Only include statuses that have cases
                status_data.append({
                    'status': status_code,
                    'label': status_label,
                    'count': count,
                    'value': value,
                    'value_formatted': self.financial_service.format_currency(value),
                    'avg_value': value / count if count > 0 else Decimal('0.00'),
                    'avg_value_formatted': self.financial_service.format_currency(value / count if count > 0 else Decimal('0.00'))
                })
        
        # Sort by count descending
        status_data.sort(key=lambda x: x['count'], reverse=True)
        
        # Key status metrics
        overdue_cases = Case.objects.filter(
            deadline__lt=self.today,
            status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
        )
        ready_to_ship = Case.objects.filter(status='ready_to_ship')
        in_progress = Case.objects.filter(status='in_progress')
        
        metrics = {
            'status_data': status_data,
            'overdue_cases': overdue_cases.count(),
            'ready_to_ship': ready_to_ship.count(),
            'in_progress': in_progress.count(),
            'ready_to_ship_count': ready_to_ship.count(),
            
            # Financial metrics for key statuses
            'overdue_value': self._calculate_case_value(overdue_cases),
            'ready_to_ship_value': self._calculate_case_value(ready_to_ship),
            'in_progress_value': self._calculate_case_value(in_progress),
            
            # Formatted values
            'overdue_value_formatted': self.financial_service.format_currency(self._calculate_case_value(overdue_cases)),
            'ready_to_ship_value_formatted': self.financial_service.format_currency(self._calculate_case_value(ready_to_ship)),
            'in_progress_value_formatted': self.financial_service.format_currency(self._calculate_case_value(in_progress)),
        }
        
        cache.set(cache_key, metrics, 300)
        return metrics


class EnhancedTrendDataService:
    """Enhanced service for generating trend data with financial metrics"""
    
    def __init__(self, reference_date: date = None):
        self.today = reference_date or timezone.now().date()
        self.financial_service = FinancialDataService()
    
    def get_enhanced_trend_data(self, period: str, range_days: int = None) -> Dict[str, Any]:
        """Get trend data with both volume and financial metrics"""
        if period == "30_days":
            return self._get_daily_trend_enhanced(range_days or 30)
        elif period == "12_months":
            return self._get_monthly_trend_enhanced(12)
        elif period == "24_months":
            return self._get_monthly_trend_enhanced(24)
        elif period == "all_time":
            return self._get_all_time_trend_enhanced()
        else:
            raise ValueError(f"Invalid period: {period}")
    
    def _get_daily_trend_enhanced(self, days: int) -> Dict[str, Any]:
        """Get daily trend data with financial metrics"""
        start_date = self.today - timedelta(days=days)
        
        # Get case counts by day
        case_trends = Case.objects.filter(
            received_date_time__date__gte=start_date
        ).annotate(
            date=TruncDay('received_date_time')
        ).values('date').annotate(
            count=Count('case_number'),
            total_value=Coalesce(Sum('cost_estimate'), Decimal('0.00'))
        ).order_by('date')
        
        # Create lookup dicts
        trend_data = {}
        value_data = {}
        for item in case_trends:
            if item['date']:
                date_str = item['date'].isoformat()
                trend_data[date_str] = item['count']
                value_data[date_str] = float(item['total_value'] or 0)
        
        # Fill missing dates
        trends = []
        for i in range(days, -1, -1):
            date = self.today - timedelta(days=i)
            date_str = date.isoformat()
            count = trend_data.get(date_str, 0)
            value = value_data.get(date_str, 0.0)
            
            trends.append({
                'date': date_str,
                'count': count,
                'value': value,
                'value_formatted': self.financial_service.format_currency(Decimal(str(value))),
                'avg_case_value': value / count if count > 0 else 0.0,
                'label': date.strftime('%b %d')
            })
        
        return {
            'data': trends,
            'json': json.dumps(trends),
            'total_count': sum(item['count'] for item in trends),
            'total_value': sum(item['value'] for item in trends),
            'total_value_formatted': self.financial_service.format_currency(Decimal(str(sum(item['value'] for item in trends))))
        }
    
    def _get_monthly_trend_enhanced(self, months: int) -> Dict[str, Any]:
        """Get monthly trend data with financial metrics"""
        start_date = self.today - relativedelta(months=months)
        
        case_trends = Case.objects.filter(
            received_date_time__date__gte=start_date
        ).annotate(
            month=TruncMonth('received_date_time')
        ).values('month').annotate(
            count=Count('case_number'),
            total_value=Coalesce(Sum('cost_estimate'), Decimal('0.00'))
        ).order_by('month')
        
        # Create lookup dicts
        trend_data = {}
        value_data = {}
        for item in case_trends:
            if item['month']:
                month_str = item['month'].isoformat()[:7]
                trend_data[month_str] = item['count']
                value_data[month_str] = float(item['total_value'] or 0)
        
        # Fill missing months
        trends = []
        for i in range(months, -1, -1):
            date = self.today - relativedelta(months=i)
            month_str = date.isoformat()[:7]
            first_day = date.replace(day=1).isoformat()
            count = trend_data.get(month_str, 0)
            value = value_data.get(month_str, 0.0)
            
            trends.append({
                'month': first_day,
                'count': count,
                'value': value,
                'value_formatted': self.financial_service.format_currency(Decimal(str(value))),
                'avg_case_value': value / count if count > 0 else 0.0,
                'label': date.strftime('%b %Y')
            })
        
        return {
            'data': trends,
            'json': json.dumps(trends),
            'total_count': sum(item['count'] for item in trends),
            'total_value': sum(item['value'] for item in trends),
            'total_value_formatted': self.financial_service.format_currency(Decimal(str(sum(item['value'] for item in trends))))
        }
    
    def _get_all_time_trend_enhanced(self) -> Dict[str, Any]:
        """Get all-time monthly trend data with financial metrics"""
        first_case = Case.objects.order_by('received_date_time').first()
        start_date = first_case.received_date_time.date() if first_case else self.today - timedelta(days=365)
        
        months_diff = ((self.today.year - start_date.year) * 12 + 
                      self.today.month - start_date.month) + 1
        
        if months_diff < 24:
            months_diff = 24
            start_date = self.today - relativedelta(months=months_diff)
        
        return self._get_monthly_trend_enhanced(months_diff)


class EnhancedDashboardService:
    """Main enhanced dashboard service with financial metrics"""
    
    def __init__(self, date_range_days: int = 30):
        self.date_range_days = date_range_days
        self.data_service = EnhancedDashboardDataService(date_range_days)
        self.trend_service = EnhancedTrendDataService(self.data_service.today)
        self.financial_service = FinancialDataService()
    
    def get_complete_enhanced_dashboard_data(self) -> Dict[str, Any]:
        """Get all dashboard data with enhanced financial metrics"""
        try:
            # Get enhanced metrics
            basic_metrics = self.data_service.get_enhanced_basic_metrics()
            status_metrics = self.data_service.get_status_metrics()
            
            # Get enhanced trend data
            trend_30_days = self.trend_service.get_enhanced_trend_data("30_days", self.date_range_days)
            trend_12_months = self.trend_service.get_enhanced_trend_data("12_months")
            trend_24_months = self.trend_service.get_enhanced_trend_data("24_months")
            trend_all_time = self.trend_service.get_enhanced_trend_data("all_time")
            
            # Other data (performance, recent cases, etc.)
            from .dashboard_service import DashboardDataService
            original_service = DashboardDataService(self.date_range_days)
            recent_data = original_service.get_recent_data()
            performance_data = original_service.get_performance_data()
            financial_metrics = original_service.get_financial_metrics()
            
            # Consolidate all data
            return {
                # Enhanced basic metrics (includes both volume and financial)
                **basic_metrics,
                **status_metrics,
                **recent_data,
                **performance_data,
                **financial_metrics,
                
                # Enhanced trend data
                'trend_30_days_data': trend_30_days['data'],
                'trend_12_months_data': trend_12_months['data'],
                'trend_24_months_data': trend_24_months['data'],
                'trend_all_time_data': trend_all_time['data'],
                
                # JSON for JavaScript (enhanced with financial data)
                'trend_30_days_json': trend_30_days['json'],
                'trend_12_months_json': trend_12_months['json'],
                'trend_24_months_json': trend_24_months['json'],
                'trend_all_time_json': trend_all_time['json'],
                
                # Chart data (enhanced with financial info)
                'status_chart_data': status_metrics['status_data'],
                'status_json': json.dumps([
                    {
                        'status': item['status'], 
                        'count': item['count'],
                        'value': float(item['value']),
                        'value_formatted': item['value_formatted']
                    }
                    for item in status_metrics['status_data']
                ]),
                
                # Summary financial data
                'trend_summaries': {
                    '30_days': {
                        'total_count': trend_30_days['total_count'],
                        'total_value': trend_30_days['total_value'],
                        'total_value_formatted': trend_30_days['total_value_formatted']
                    },
                    '12_months': {
                        'total_count': trend_12_months['total_count'],
                        'total_value': trend_12_months['total_value'],
                        'total_value_formatted': trend_12_months['total_value_formatted']
                    }
                },
                
                # Meta data
                'selected_range': str(self.date_range_days),
                'today': self.data_service.today.strftime('%B %d, %Y'),
                'currency': self.financial_service.base_currency,
            }
            
        except Exception as e:
            logger.error(f"Error getting enhanced dashboard data: {e}")
            return self._get_fallback_data()
    
    def _get_fallback_data(self) -> Dict[str, Any]:
        """Return minimal data structure when errors occur"""
        return {
            'total_cases': 0,
            'cases_today': 0,
            'cases_this_week': 0,
            'cases_this_month': 0,
            'month_growth': 0,
            'total_value': 0,
            'today_value': 0,
            'week_value': 0,
            'month_value': 0,
            'total_value_formatted': '$0.00',
            'today_value_formatted': '$0.00',
            'week_value_formatted': '$0.00',
            'month_value_formatted': '$0.00',
            'overdue_cases': 0,
            'ready_to_ship': 0,
            'in_progress': 0,
            'recent_cases': [],
            'upcoming_deadlines': [],
            'trend_30_days_data': [],
            'trend_12_months_data': [],
            'trend_24_months_data': [],
            'trend_all_time_data': [],
            'trend_30_days_json': '[]',
            'trend_12_months_json': '[]',
            'trend_24_months_json': '[]',
            'trend_all_time_json': '[]',
            'status_chart_data': [],
            'status_json': '[]',
            'selected_range': str(self.date_range_days),
            'today': timezone.now().date().strftime('%B %d, %Y'),
            'currency': 'USD',
            'error': 'Unable to load enhanced dashboard data',
        }
