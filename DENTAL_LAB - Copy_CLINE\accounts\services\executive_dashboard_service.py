"""
Executive Dashboard Service
Professional financial analytics for dental lab management
Focuses on invoiced, receivable, and income metrics
"""

from django.db.models import Sum, Count, Q, Avg, F, Case as DbCase, When, Value
from django.db.models.functions import Coalesce, TruncDay, Trunc<PERSON>onth
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import logging

from case.models import Case
from billing.models import Invoice
from finance.models import Payment, InvoicePayment
from items.models import Currency

logger = logging.getLogger(__name__)


class ExecutiveDashboardService:
    """
    Executive-level dashboard service providing comprehensive financial and operational insights
    """
    
    def __init__(self, date_range_days=30):
        self.date_range_days = date_range_days
        self.end_date = timezone.now()
        self.start_date = self.end_date - timedelta(days=date_range_days)
        
    def get_comprehensive_dashboard_data(self):
        """Get all dashboard data for executive view"""
        try:
            # Get financial metrics
            financial_metrics = self._get_financial_performance()
            
            # Get operational metrics
            operational_metrics = self._get_operational_performance()
            
            # Get status breakdown with values
            status_breakdown = self._get_status_financial_breakdown()
            
            # Get recent financial activities
            recent_activities = self._get_recent_financial_activities()
            
            # Get trending data
            trends = self._get_financial_trends()
            
            # Get productivity metrics
            productivity = self._get_productivity_metrics()
            
            return {
                **financial_metrics,
                **operational_metrics,
                'status_breakdown': status_breakdown,
                'recent_activities': recent_activities,
                'trends': trends,
                'productivity': productivity,
                'date_range_days': self.date_range_days,
                'period_description': self._get_period_description(),
            }
            
        except Exception as e:
            logger.error(f"Executive dashboard error: {e}")
            return self._get_fallback_data()
    
    def _get_financial_performance(self):
        """Core financial metrics: Invoiced, Receivable, Income"""
        
        # Cases in date range
        cases_query = Case.objects.filter(
            created_at__gte=self.start_date,
            created_at__lte=self.end_date
        )
        
        # Get invoices for cases in range
        invoices_query = Invoice.objects.filter(
            case__in=cases_query
        ).select_related('case', 'currency')
        
        # Total invoiced amount (all invoices generated)
        total_invoiced = invoices_query.aggregate(
            total=Coalesce(Sum('total_amount'), Decimal('0'))
        )['total']
        
        # Outstanding receivables (unpaid and partial invoices)
        # Calculate based on invoice status since outstanding_amount field doesn't exist
        total_receivable = invoices_query.filter(
            status__in=['unpaid', 'partial']
        ).aggregate(
            total=Coalesce(Sum('total_amount'), Decimal('0'))
        )['total']
        
        # Income received (paid invoices)
        total_income = invoices_query.filter(
            status='paid'
        ).aggregate(
            total=Coalesce(Sum('total_amount'), Decimal('0'))
        )['total']
        
        # Alternative calculation for more accurate receivables
        # This would be more accurate if you have detailed payment tracking
        try:
            # Calculate actual outstanding by subtracting payments from total
            from finance.models import InvoicePayment
            
            paid_amounts = InvoicePayment.objects.filter(
                invoice__in=invoices_query
            ).aggregate(
                total_paid=Coalesce(Sum('amount'), Decimal('0'))
            )['total_paid']
            
            # Recalculate receivable as invoiced minus paid
            total_receivable = total_invoiced - paid_amounts
            total_income = paid_amounts
            
        except Exception as e:
            # Fall back to status-based calculation if InvoicePayment not available
            logger.warning(f"Could not calculate precise receivables: {e}")
        
        # Average case value
        case_count = cases_query.count()
        avg_case_value = total_invoiced / case_count if case_count > 0 else Decimal('0')
        
        # Collection rate
        collection_rate = (total_income / total_invoiced * 100) if total_invoiced > 0 else Decimal('0')
        
        # Outstanding rate
        outstanding_rate = (total_receivable / total_invoiced * 100) if total_invoiced > 0 else Decimal('0')
        
        return {
            'total_invoiced': total_invoiced,
            'total_receivable': total_receivable,
            'total_income': total_income,
            'avg_case_value': avg_case_value,
            'collection_rate': collection_rate,
            'outstanding_rate': outstanding_rate,
            'case_count': case_count,
        }
    
    def _get_operational_performance(self):
        """Operational metrics for business performance"""
        
        cases_query = Case.objects.filter(
            created_at__gte=self.start_date,
            created_at__lte=self.end_date
        )
        
        # Case status counts
        status_counts = cases_query.values('status').annotate(
            count=Count('case_number')  # Use case_number instead of id
        ).order_by('-count')
        
        # Completion metrics
        completed_cases = cases_query.filter(status='completed').count()
        completion_rate = (completed_cases / cases_query.count() * 100) if cases_query.count() > 0 else 0
        
        # Overdue cases
        overdue_cases = cases_query.filter(
            estimated_completion__lt=timezone.now(),
            status__in=['pending_acceptance', 'in_progress', 'quality_check', 'revision_needed']
        ).count()
        
        # Ready to ship
        ready_to_ship = cases_query.filter(status='ready_to_ship').count()
        
        # Average completion time
        completed_cases_with_time = cases_query.filter(
            status='completed',
            estimated_completion__isnull=False,
            actual_completion__isnull=False  # Use actual_completion instead of completed_at
        )
        
        if completed_cases_with_time.exists():
            avg_completion_days = completed_cases_with_time.aggregate(
                avg_days=Avg(
                    F('actual_completion') - F('created_at')
                )
            )['avg_days']
            avg_completion_days = avg_completion_days.days if avg_completion_days else 0
        else:
            avg_completion_days = 0
        
        return {
            'total_cases': cases_query.count(),
            'completed_cases': completed_cases,
            'completion_rate': completion_rate,
            'overdue_cases': overdue_cases,
            'ready_to_ship': ready_to_ship,
            'avg_completion_days': avg_completion_days,
            'status_distribution': {item['status']: item['count'] for item in status_counts},
        }
    
    def _get_status_financial_breakdown(self):
        """Financial breakdown by case status"""
        
        cases_query = Case.objects.filter(
            created_at__gte=self.start_date,
            created_at__lte=self.end_date
        )
        
        # Aggregate by status with financial data
        status_breakdown = cases_query.values('status').annotate(
            case_count=Count('case_number'),  # Use case_number instead of id
            total_value=Coalesce(Sum('cost_estimate'), Decimal('0')),  # Use cost_estimate instead of total_cost
            avg_value=Coalesce(Avg('cost_estimate'), Decimal('0'))
        ).order_by('-total_value')
        
        # Add invoice data for each status
        for status_data in status_breakdown:
            status_cases = cases_query.filter(status=status_data['status'])
            
            invoiced = Invoice.objects.filter(
                case__in=status_cases
            ).aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0'))
            )['total']
            
            status_data['invoiced_amount'] = invoiced
            status_data['status_display'] = dict(Case.STATUS_CHOICES).get(
                status_data['status'], 
                status_data['status'].replace('_', ' ').title()
            )
        
        return list(status_breakdown)
    
    def _get_recent_financial_activities(self):
        """Recent invoices and payments for activity feed"""
        
        # Recent invoices
        recent_invoices = Invoice.objects.filter(
            created_at__gte=self.start_date - timedelta(days=7)  # Last week
        ).select_related('case', 'dentist').order_by('-created_at')[:10]
        
        # Recent payments
        recent_payments = Payment.objects.filter(
            date__gte=self.start_date - timedelta(days=7)  # Use 'date' instead of 'payment_date'
        ).select_related('account', 'currency').order_by('-date')[:10]
        
        activities = []
        
        # Add invoice activities
        for invoice in recent_invoices:
            activities.append({
                'type': 'invoice',
                'date': invoice.created_at,
                'description': f"Invoice #{invoice.invoice_number} for Case #{invoice.case.case_number}",
                'amount': invoice.total_amount,
                'status': invoice.status,
                'case_id': invoice.case.id if invoice.case else None,
            })
        
        # Add payment activities
        for payment in recent_payments:
            activities.append({
                'type': 'payment',
                'date': payment.date,  # Use 'date' instead of 'payment_date'
                'description': f"Payment received - {payment.reference or 'Payment'}",  # Use 'reference' instead of 'description'
                'amount': payment.amount,
                'method': payment.payment_method,
                'account': payment.account.name if payment.account else 'Unknown',
            })
        
        # Sort by date
        activities.sort(key=lambda x: x['date'], reverse=True)
        
        return activities[:15]  # Return top 15 activities
    
    def _get_financial_trends(self):
        """Financial trends over time"""
        
        # Daily trends for the selected period
        daily_trends = Case.objects.filter(
            created_at__gte=self.start_date,
            created_at__lte=self.end_date
        ).annotate(
            date=TruncDay('created_at')
        ).values('date').annotate(
            case_count=Count('case_number'),  # Use case_number instead of id
            total_value=Coalesce(Sum('cost_estimate'), Decimal('0'))  # Use cost_estimate
        ).order_by('date')
        
        # Monthly trends for longer periods
        monthly_trends = Case.objects.filter(
            created_at__gte=self.start_date - timedelta(days=365)  # Last year
        ).annotate(
            month=TruncMonth('created_at')
        ).values('month').annotate(
            case_count=Count('case_number'),  # Use case_number instead of id
            total_value=Coalesce(Sum('cost_estimate'), Decimal('0'))  # Use cost_estimate
        ).order_by('month')
        
        return {
            'daily': list(daily_trends),
            'monthly': list(monthly_trends),
        }
    
    def _get_productivity_metrics(self):
        """Productivity and efficiency metrics"""
        
        cases_query = Case.objects.filter(
            created_at__gte=self.start_date,
            created_at__lte=self.end_date
        )
        
        # Cases per day
        days_in_period = (self.end_date - self.start_date).days
        cases_per_day = cases_query.count() / days_in_period if days_in_period > 0 else 0
        
        # Revenue per day
        total_value = cases_query.aggregate(
            total=Coalesce(Sum('cost_estimate'), Decimal('0'))  # Use cost_estimate
        )['total']
        revenue_per_day = total_value / days_in_period if days_in_period > 0 else Decimal('0')
        
        # On-time delivery rate
        completed_on_time = cases_query.filter(
            status='completed',
            actual_completion__lte=F('estimated_completion')  # Use actual_completion
        ).count()
        
        total_completed = cases_query.filter(status='completed').count()
        on_time_rate = (completed_on_time / total_completed * 100) if total_completed > 0 else 0
        
        return {
            'cases_per_day': round(cases_per_day, 1),
            'revenue_per_day': revenue_per_day,
            'on_time_rate': round(on_time_rate, 1),
            'productivity_score': self._calculate_productivity_score(cases_per_day, on_time_rate),
        }
    
    def _calculate_productivity_score(self, cases_per_day, on_time_rate):
        """Calculate overall productivity score (0-100)"""
        # Weighted score: 60% volume, 40% quality
        volume_score = min(cases_per_day * 10, 100)  # Assume 10 cases/day = 100%
        quality_score = on_time_rate
        
        return round((volume_score * 0.6) + (quality_score * 0.4), 1)
    
    def _get_period_description(self):
        """Human-readable period description"""
        if self.date_range_days == 7:
            return "Last 7 Days"
        elif self.date_range_days == 30:
            return "Last 30 Days"
        elif self.date_range_days == 90:
            return "Last 3 Months"
        elif self.date_range_days == 180:
            return "Last 6 Months"
        elif self.date_range_days == 365:
            return "Last 12 Months"
        else:
            return f"Last {self.date_range_days} Days"
    
    def _get_fallback_data(self):
        """Fallback data in case of errors"""
        return {
            'total_invoiced': Decimal('0'),
            'total_receivable': Decimal('0'),
            'total_income': Decimal('0'),
            'avg_case_value': Decimal('0'),
            'collection_rate': Decimal('0'),
            'outstanding_rate': Decimal('0'),
            'case_count': 0,
            'total_cases': 0,
            'completed_cases': 0,
            'completion_rate': 0,
            'overdue_cases': 0,
            'ready_to_ship': 0,
            'avg_completion_days': 0,
            'status_distribution': {},
            'status_breakdown': [],
            'recent_activities': [],
            'trends': {'daily': [], 'monthly': []},
            'productivity': {
                'cases_per_day': 0,
                'revenue_per_day': Decimal('0'),
                'on_time_rate': 0,
                'productivity_score': 0,
            },
            'date_range_days': self.date_range_days,
            'period_description': self._get_period_description(),
            'error': True,
        }
