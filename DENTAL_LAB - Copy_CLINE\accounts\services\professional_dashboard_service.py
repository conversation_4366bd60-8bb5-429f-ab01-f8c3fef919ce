"""
Professional Financial Dashboard Service
Tracks Invoiced, Receivable, and Income with sophisticated analytics
"""

from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta
import json
import logging
from decimal import Decimal

from django.utils import timezone
from django.db.models import Count, Sum, Avg, Q, Max, F, Case as DBCase, When, DecimalField
from django.db.models.functions import <PERSON>runcDay, <PERSON>runcMonth, Coalesce
from django.core.cache import cache
from django.conf import settings

from case.models import Case, Department, Task
from Dentists.models import Dentist
from billing.models import Invoice
from finance.models import Payment
from patients.models import Patient

logger = logging.getLogger(__name__)


class ProfessionalFinancialService:
    """Professional financial analysis service with proper accounting terminology"""
    
    def __init__(self, base_currency: str = 'USD'):
        self.base_currency = base_currency
        self.exchange_rates = self._get_exchange_rates()
    
    def _get_exchange_rates(self) -> Dict[str, float]:
        """Get current exchange rates for multi-currency support"""
        return {
            'USD': 1.0,
            'EUR': 0.85,
            'GBP': 0.73,
            'ALL': 100.0,  # Albanian Lek
            'CAD': 1.25,
            'AUD': 1.35,
        }
    
    def calculate_financial_metrics(self, cases_queryset=None, date_range=None) -> Dict[str, Decimal]:
        """
        Calculate comprehensive financial metrics
        Returns: invoiced, receivable, income, collection_rate, avg_case_value
        """
        if cases_queryset is None:
            cases_queryset = Case.objects.all()
        
        case_numbers = list(cases_queryset.values_list('case_number', flat=True))
        
        # Get invoicing data
        invoices_qs = Invoice.objects.filter(case_id__in=case_numbers)
        if date_range:
            invoices_qs = invoices_qs.filter(date__gte=date_range)
        
        # Total invoiced amount
        total_invoiced = invoices_qs.aggregate(
            total=Coalesce(Sum('total_amount'), Decimal('0.00'))
        )['total'] or Decimal('0.00')
        
        # Payments received (income)
        payments_qs = Payment.objects.filter(
            invoice__case_id__in=case_numbers
        )
        if date_range:
            payments_qs = payments_qs.filter(date__gte=date_range)
            
        total_income = payments_qs.aggregate(
            total=Coalesce(Sum('amount'), Decimal('0.00'))
        )['total'] or Decimal('0.00')
        
        # Outstanding receivables
        total_receivable = total_invoiced - total_income
        
        # Collection rate
        collection_rate = (total_income / total_invoiced * 100) if total_invoiced > 0 else Decimal('0.00')
        
        # Average case value (based on invoiced amounts)
        case_count = cases_queryset.count()
        avg_case_value = total_invoiced / case_count if case_count > 0 else Decimal('0.00')
        
        # Days Sales Outstanding (DSO) - simplified calculation
        days_in_period = 30 if not date_range else (timezone.now().date() - date_range).days
        dso = (total_receivable / (total_invoiced / days_in_period)) if total_invoiced > 0 else Decimal('0.00')
        
        return {
            'total_invoiced': total_invoiced,
            'total_income': total_income,
            'total_receivable': total_receivable,
            'collection_rate': collection_rate.quantize(Decimal('0.01')),
            'avg_case_value': avg_case_value.quantize(Decimal('0.01')),
            'days_sales_outstanding': dso.quantize(Decimal('0.1')),
            'case_count': case_count
        }
    
    def format_currency(self, amount: Decimal, currency: str = None) -> str:
        """Format currency with appropriate symbol and formatting"""
        if currency is None:
            currency = self.base_currency
            
        symbols = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£', 
            'ALL': 'L',
            'CAD': 'C$',
            'AUD': 'A$',
        }
        
        symbol = symbols.get(currency, currency + ' ')
        
        if currency == 'ALL':
            return f"{symbol}{amount:,.0f}"
        else:
            return f"{symbol}{amount:,.2f}"


class ProfessionalDashboardService:
    """Professional dashboard service with sophisticated financial analytics"""
    
    def __init__(self, date_range_days: int = 30):
        self.date_range_days = date_range_days
        self.today = timezone.now().date()
        self.financial_service = ProfessionalFinancialService()
        self._setup_date_ranges()
    
    def _setup_date_ranges(self):
        """Setup date ranges for analysis"""
        self.start_of_week = self.today - timedelta(days=self.today.weekday())
        self.start_of_month = self.today.replace(day=1)
        self.start_of_last_month = (self.start_of_month - timedelta(days=1)).replace(day=1)
        self.start_of_quarter = self._get_quarter_start(self.today)
        self.start_of_year = self.today.replace(month=1, day=1)
        self.range_start_date = self.today - timedelta(days=self.date_range_days)
        
        # Adjust for data availability
        self._adjust_for_data_availability()
    
    def _get_quarter_start(self, date: date) -> date:
        """Get the start of the current quarter"""
        quarter = (date.month - 1) // 3 + 1
        return date.replace(month=(quarter - 1) * 3 + 1, day=1)
    
    def _adjust_for_data_availability(self):
        """Adjust date ranges based on actual data availability"""
        has_recent_data = Case.objects.filter(
            received_date_time__date__gte=self.today - timedelta(days=30)
        ).exists()
        
        if not has_recent_data:
            latest_case_date = Case.objects.filter(
                received_date_time__isnull=False
            ).aggregate(latest=Max('received_date_time'))['latest']
            
            if latest_case_date:
                self.today = latest_case_date.date()
                logger.info(f"Adjusted dashboard reference date to {self.today}")
                self._setup_date_ranges()
    
    def get_executive_summary(self) -> Dict[str, Any]:
        """Get executive-level financial summary"""
        cache_key = f"executive_summary_{self.today}_{self.date_range_days}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        # Current period metrics
        current_metrics = self.financial_service.calculate_financial_metrics(
            date_range=self.range_start_date
        )
        
        # Month-to-date metrics
        mtd_metrics = self.financial_service.calculate_financial_metrics(
            date_range=self.start_of_month
        )
        
        # Quarter-to-date metrics
        qtd_metrics = self.financial_service.calculate_financial_metrics(
            date_range=self.start_of_quarter
        )
        
        # Year-to-date metrics
        ytd_metrics = self.financial_service.calculate_financial_metrics(
            date_range=self.start_of_year
        )
        
        # Previous month for comparison
        prev_month_metrics = self.financial_service.calculate_financial_metrics(
            cases_queryset=Case.objects.filter(
                received_date_time__date__gte=self.start_of_last_month,
                received_date_time__date__lt=self.start_of_month
            )
        )
        
        # Calculate growth rates
        mtd_growth = self._calculate_growth_rate(
            mtd_metrics['total_invoiced'], 
            prev_month_metrics['total_invoiced']
        )
        
        income_growth = self._calculate_growth_rate(
            mtd_metrics['total_income'],
            prev_month_metrics['total_income']
        )
        
        summary = {
            # Executive KPIs
            'total_cases': Case.objects.count(),
            'active_cases': Case.objects.filter(
                status__in=['pending_acceptance', 'in_progress', 'quality_check']
            ).count(),
            
            # Financial Performance
            'mtd_invoiced': mtd_metrics['total_invoiced'],
            'mtd_income': mtd_metrics['total_income'],
            'mtd_receivable': mtd_metrics['total_receivable'],
            'qtd_invoiced': qtd_metrics['total_invoiced'],
            'ytd_invoiced': ytd_metrics['total_invoiced'],
            'ytd_income': ytd_metrics['total_income'],
            
            # Key Ratios
            'collection_rate': mtd_metrics['collection_rate'],
            'avg_case_value': mtd_metrics['avg_case_value'],
            'days_sales_outstanding': mtd_metrics['days_sales_outstanding'],
            
            # Growth Metrics
            'mtd_invoiced_growth': mtd_growth,
            'income_growth': income_growth,
            
            # Formatted Display Values
            'mtd_invoiced_formatted': self.financial_service.format_currency(mtd_metrics['total_invoiced']),
            'mtd_income_formatted': self.financial_service.format_currency(mtd_metrics['total_income']),
            'mtd_receivable_formatted': self.financial_service.format_currency(mtd_metrics['total_receivable']),
            'qtd_invoiced_formatted': self.financial_service.format_currency(qtd_metrics['total_invoiced']),
            'ytd_invoiced_formatted': self.financial_service.format_currency(ytd_metrics['total_invoiced']),
            'ytd_income_formatted': self.financial_service.format_currency(ytd_metrics['total_income']),
            'avg_case_value_formatted': self.financial_service.format_currency(mtd_metrics['avg_case_value']),
        }
        
        cache.set(cache_key, summary, 300)
        return summary
    
    def get_operational_metrics(self) -> Dict[str, Any]:
        """Get operational performance metrics"""
        cache_key = f"operational_metrics_{self.today}_{self.date_range_days}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        # Case status distribution with financial impact
        status_analysis = []
        for status_choice in Case.STATUS_CHOICES:
            status_code = status_choice[0]
            status_label = status_choice[1]
            
            status_cases = Case.objects.filter(status=status_code)
            if status_cases.exists():
                financial_metrics = self.financial_service.calculate_financial_metrics(status_cases)
                
                status_analysis.append({
                    'status': status_code,
                    'label': status_label,
                    'case_count': financial_metrics['case_count'],
                    'total_invoiced': financial_metrics['total_invoiced'],
                    'total_income': financial_metrics['total_income'],
                    'total_receivable': financial_metrics['total_receivable'],
                    'avg_case_value': financial_metrics['avg_case_value'],
                    'invoiced_formatted': self.financial_service.format_currency(financial_metrics['total_invoiced']),
                    'income_formatted': self.financial_service.format_currency(financial_metrics['total_income']),
                    'receivable_formatted': self.financial_service.format_currency(financial_metrics['total_receivable']),
                    'avg_case_value_formatted': self.financial_service.format_currency(financial_metrics['avg_case_value']),
                })
        
        # Sort by total invoiced amount
        status_analysis.sort(key=lambda x: x['total_invoiced'], reverse=True)
        
        # Performance alerts
        overdue_cases = Case.objects.filter(
            deadline__lt=self.today,
            status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
        )
        overdue_financial = self.financial_service.calculate_financial_metrics(overdue_cases)
        
        high_value_cases = Case.objects.filter(
            cost_estimate__gte=100  # Cases over $100
        )
        high_value_financial = self.financial_service.calculate_financial_metrics(high_value_cases)
        
        metrics = {
            'status_analysis': status_analysis,
            'overdue_cases': overdue_cases.count(),
            'overdue_value': overdue_financial['total_invoiced'],
            'overdue_value_formatted': self.financial_service.format_currency(overdue_financial['total_invoiced']),
            'high_value_cases': high_value_cases.count(),
            'high_value_amount': high_value_financial['total_invoiced'],
            'high_value_amount_formatted': self.financial_service.format_currency(high_value_financial['total_invoiced']),
            'ready_to_ship': Case.objects.filter(status='ready_to_ship').count(),
            'in_production': Case.objects.filter(status='in_progress').count(),
        }
        
        cache.set(cache_key, metrics, 300)
        return metrics
    
    def get_financial_trends(self, period: str = "30_days") -> Dict[str, Any]:
        """Get financial trends with invoiced, income, and receivable tracking"""
        if period == "30_days":
            return self._get_daily_financial_trends(self.date_range_days)
        elif period in ["12_months", "24_months"]:
            months = int(period.split("_")[0])
            return self._get_monthly_financial_trends(months)
        else:
            return self._get_monthly_financial_trends(12)
    
    def _get_daily_financial_trends(self, days: int) -> Dict[str, Any]:
        """Get daily financial trends"""
        start_date = self.today - timedelta(days=days)
        
        trends = []
        for i in range(days, -1, -1):
            date = self.today - timedelta(days=i)
            
            # Get cases for this day
            day_cases = Case.objects.filter(received_date_time__date=date)
            
            if day_cases.exists():
                financial_metrics = self.financial_service.calculate_financial_metrics(day_cases)
                
                trends.append({
                    'date': date.isoformat(),
                    'label': date.strftime('%b %d'),
                    'cases': financial_metrics['case_count'],
                    'invoiced': float(financial_metrics['total_invoiced']),
                    'income': float(financial_metrics['total_income']),
                    'receivable': float(financial_metrics['total_receivable']),
                    'invoiced_formatted': self.financial_service.format_currency(financial_metrics['total_invoiced']),
                    'income_formatted': self.financial_service.format_currency(financial_metrics['total_income']),
                    'receivable_formatted': self.financial_service.format_currency(financial_metrics['total_receivable']),
                })
            else:
                trends.append({
                    'date': date.isoformat(),
                    'label': date.strftime('%b %d'),
                    'cases': 0,
                    'invoiced': 0.0,
                    'income': 0.0,
                    'receivable': 0.0,
                    'invoiced_formatted': self.financial_service.format_currency(Decimal('0.00')),
                    'income_formatted': self.financial_service.format_currency(Decimal('0.00')),
                    'receivable_formatted': self.financial_service.format_currency(Decimal('0.00')),
                })
        
        # Calculate totals
        total_invoiced = sum(item['invoiced'] for item in trends)
        total_income = sum(item['income'] for item in trends)
        total_receivable = sum(item['receivable'] for item in trends)
        
        return {
            'data': trends,
            'json': json.dumps(trends),
            'period_summary': {
                'total_invoiced': total_invoiced,
                'total_income': total_income,
                'total_receivable': total_receivable,
                'total_invoiced_formatted': self.financial_service.format_currency(Decimal(str(total_invoiced))),
                'total_income_formatted': self.financial_service.format_currency(Decimal(str(total_income))),
                'total_receivable_formatted': self.financial_service.format_currency(Decimal(str(total_receivable))),
            }
        }
    
    def _get_monthly_financial_trends(self, months: int) -> Dict[str, Any]:
        """Get monthly financial trends"""
        start_date = self.today - relativedelta(months=months)
        
        trends = []
        for i in range(months, -1, -1):
            date = self.today - relativedelta(months=i)
            month_start = date.replace(day=1)
            month_end = (month_start + relativedelta(months=1)) - timedelta(days=1)
            
            # Get cases for this month
            month_cases = Case.objects.filter(
                received_date_time__date__gte=month_start,
                received_date_time__date__lte=month_end
            )
            
            financial_metrics = self.financial_service.calculate_financial_metrics(month_cases)
            
            trends.append({
                'month': month_start.isoformat(),
                'label': date.strftime('%b %Y'),
                'cases': financial_metrics['case_count'],
                'invoiced': float(financial_metrics['total_invoiced']),
                'income': float(financial_metrics['total_income']),
                'receivable': float(financial_metrics['total_receivable']),
                'collection_rate': float(financial_metrics['collection_rate']),
                'avg_case_value': float(financial_metrics['avg_case_value']),
                'invoiced_formatted': self.financial_service.format_currency(financial_metrics['total_invoiced']),
                'income_formatted': self.financial_service.format_currency(financial_metrics['total_income']),
                'receivable_formatted': self.financial_service.format_currency(financial_metrics['total_receivable']),
            })
        
        return {
            'data': trends,
            'json': json.dumps(trends),
        }
    
    def _calculate_growth_rate(self, current: Decimal, previous: Decimal) -> Decimal:
        """Calculate growth rate percentage"""
        if previous == 0:
            return Decimal('0.00')
        return ((current - previous) / previous * 100).quantize(Decimal('0.1'))
    
    def get_recent_activity(self) -> Dict[str, Any]:
        """Get recent cases and important notifications"""
        recent_cases = Case.objects.select_related('dentist', 'patient').filter(
            received_date_time__date__gte=self.range_start_date
        ).order_by('-received_date_time')[:8]
        
        upcoming_deadlines = Case.objects.filter(
            deadline__gte=self.today,
            deadline__lte=self.today + timedelta(days=7),
            status__in=['pending_acceptance', 'in_progress', 'on_hold']
        ).order_by('deadline')[:6]
        
        return {
            'recent_cases': recent_cases,
            'upcoming_deadlines': upcoming_deadlines,
        }
    
    def get_complete_dashboard_data(self) -> Dict[str, Any]:
        """Get complete professional dashboard data"""
        try:
            executive_summary = self.get_executive_summary()
            operational_metrics = self.get_operational_metrics()
            financial_trends = self.get_financial_trends("30_days")
            monthly_trends = self.get_financial_trends("12_months")
            recent_activity = self.get_recent_activity()
            
            return {
                **executive_summary,
                **operational_metrics,
                **recent_activity,
                'financial_trends_30d': financial_trends,
                'financial_trends_12m': monthly_trends,
                'trends_30_days_data': financial_trends['data'],
                'trends_12_months_data': monthly_trends['data'],
                'trends_30_days_json': financial_trends['json'],
                'trends_12_months_json': monthly_trends['json'],
                'selected_range': str(self.date_range_days),
                'today': self.today.strftime('%B %d, %Y'),
                'currency': self.financial_service.base_currency,
                'dashboard_type': 'professional'
            }
            
        except Exception as e:
            logger.error(f"Error getting professional dashboard data: {e}")
            return self._get_fallback_data()
    
    def _get_fallback_data(self) -> Dict[str, Any]:
        """Fallback data structure"""
        return {
            'total_cases': 0,
            'active_cases': 0,
            'mtd_invoiced_formatted': '$0.00',
            'mtd_income_formatted': '$0.00',
            'mtd_receivable_formatted': '$0.00',
            'collection_rate': Decimal('0.00'),
            'status_analysis': [],
            'trends_30_days_data': [],
            'trends_12_months_data': [],
            'trends_30_days_json': '[]',
            'trends_12_months_json': '[]',
            'recent_cases': [],
            'upcoming_deadlines': [],
            'error': 'Unable to load dashboard data',
            'dashboard_type': 'professional'
        }
