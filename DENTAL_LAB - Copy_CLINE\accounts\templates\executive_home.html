{% extends "base.html" %}
{% load static humanize mathfilters %}

{% block title %}Executive Dashboard | Dental Lab Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
:root {
    /* Professional Color Palette */
    --primary-500: #2563eb;
    --primary-600: #1d4ed8;
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    
    --success-500: #10b981;
    --success-50: #ecfdf5;
    --success-100: #d1fae5;
    
    --warning-500: #f59e0b;
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    
    --danger-500: #ef4444;
    --danger-50: #fef2f2;
    --danger-100: #fee2e2;
    
    --info-500: #06b6d4;
    --info-50: #f0fdfa;
    --info-100: #ccfbf1;
    
    --purple-500: #8b5cf6;
    --purple-50: #faf5ff;
    --purple-100: #e9d5ff;
    
    /* Neutrals */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Semantic Colors */
    --background: #ffffff;
    --surface: #ffffff;
    --surface-secondary: var(--gray-50);
    --border: var(--gray-200);
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-muted: var(--gray-500);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    
    /* Typography */
    --font-family: 'Inter', system-ui, -apple-system, sans-serif;
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    
    /* Border Radius */
    --rounded-sm: 0.125rem;
    --rounded: 0.25rem;
    --rounded-md: 0.375rem;
    --rounded-lg: 0.5rem;
    --rounded-xl: 0.75rem;
    --rounded-2xl: 1rem;
}

/* Dark Mode */
[data-theme="dark"] {
    --background: var(--gray-900);
    --surface: var(--gray-800);
    --surface-secondary: var(--gray-700);
    --border: var(--gray-600);
    --text-primary: var(--gray-100);
    --text-secondary: var(--gray-300);
    --text-muted: var(--gray-400);
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--surface-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Layout */
.executive-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-6) var(--space-4);
}

/* Header */
.executive-header {
    background: var(--background);
    border-bottom: 1px solid var(--border);
    margin: calc(-1 * var(--space-6)) calc(-1 * var(--space-4)) var(--space-8);
    padding: var(--space-6) var(--space-4);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-4);
}

.header-title-section h1 {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--space-1);
}

.header-title-section p {
    color: var(--text-secondary);
    margin: 0;
    font-size: var(--text-base);
}

.header-controls {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.period-selector {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--rounded-lg);
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-sm);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.period-selector:hover {
    border-color: var(--primary-500);
}

.refresh-btn {
    background: var(--primary-500);
    color: white;
    border: none;
    border-radius: var(--rounded-lg);
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.refresh-btn:hover {
    background: var(--primary-600);
}

/* Financial Overview Cards */
.financial-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.finance-card {
    background: var(--background);
    border: 1px solid var(--border);
    border-radius: var(--rounded-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.finance-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.finance-card-header {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.finance-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--rounded-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
}

.finance-icon.invoiced {
    background: var(--primary-100);
    color: var(--primary-600);
}

.finance-icon.receivable {
    background: var(--warning-100);
    color: var(--warning-600);
}

.finance-icon.income {
    background: var(--success-100);
    color: var(--success-600);
}

.finance-icon.average {
    background: var(--info-100);
    color: var(--info-600);
}

.finance-card-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.finance-card-subtitle {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

.finance-value {
    font-size: var(--text-4xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.finance-meta {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.finance-percentage {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-weight: 500;
}

.finance-percentage.positive {
    color: var(--success-500);
}

.finance-percentage.negative {
    color: var(--danger-500);
}

.finance-percentage.neutral {
    color: var(--text-muted);
}

/* Responsive */
@media (max-width: 768px) {
    .executive-container {
        padding: var(--space-4) var(--space-3);
    }
    
    .executive-header {
        margin: calc(-1 * var(--space-4)) calc(-1 * var(--space-3)) var(--space-6);
        padding: var(--space-4) var(--space-3);
    }
    
    .header-content {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .header-title-section h1 {
        font-size: var(--text-2xl);
    }
    
    .financial-overview {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
}

@media (max-width: 480px) {
    .finance-value {
        font-size: var(--text-3xl);
    }
}
</style>
{% endblock %}
{% block content %}
<div class="executive-container">
    <!-- Header -->
    <div class="executive-header">
        <div class="header-content">
            <div class="header-title-section">
                <h1>Executive Dashboard</h1>
                <p>Financial Performance & Operational Insights • {{ period_description|default:"Last 30 Days" }}</p>
            </div>
            <div class="header-controls">
                <select class="period-selector" id="periodSelector" onchange="updateDashboard()">
                    <option value="7" {% if selected_range == '7' %}selected{% endif %}>Last 7 Days</option>
                    <option value="30" {% if selected_range == '30' %}selected{% endif %}>Last 30 Days</option>
                    <option value="90" {% if selected_range == '90' %}selected{% endif %}>Last 3 Months</option>
                    <option value="180" {% if selected_range == '180' %}selected{% endif %}>Last 6 Months</option>
                    <option value="365" {% if selected_range == '365' %}selected{% endif %}>Last 12 Months</option>
                </select>
                <button class="refresh-btn" onclick="refreshDashboard()">
                    <i class="bi bi-arrow-clockwise"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    {% if error %}
    <!-- Error Banner -->
    <div style="background: var(--danger-50); border: 1px solid var(--danger-200); border-radius: var(--rounded-lg); padding: var(--space-4); margin-bottom: var(--space-6); display: flex; align-items: center; gap: var(--space-3);">
        <i class="bi bi-exclamation-triangle-fill" style="color: var(--danger-500);"></i>
        <div style="flex: 1;">
            <div style="font-weight: 600; color: var(--danger-700); margin: 0 0 var(--space-1);">Dashboard Error</div>
            <p style="color: var(--danger-600); font-size: var(--text-sm); margin: 0;">{{ error }} Please check system logs or contact support.</p>
        </div>
    </div>
    {% endif %}

    <!-- Financial Overview -->
    <div class="financial-overview">
        <div class="finance-card">
            <div class="finance-card-header">
                <div class="finance-icon invoiced">
                    <i class="bi bi-receipt"></i>
                </div>
                <div>
                    <h3 class="finance-card-title">Total Invoiced</h3>
                    <p class="finance-card-subtitle">Amount billed to clients</p>
                </div>
            </div>
            <div class="finance-value">${{ total_invoiced|floatformat:0|intcomma }}</div>
            <div class="finance-meta">
                <span>{{ case_count|intcomma }} cases</span>
                <span class="finance-percentage positive">
                    <i class="bi bi-arrow-up-short"></i>
                    Business metric
                </span>
            </div>
        </div>

        <div class="finance-card">
            <div class="finance-card-header">
                <div class="finance-icon receivable">
                    <i class="bi bi-clock-history"></i>
                </div>
                <div>
                    <h3 class="finance-card-title">Outstanding Receivable</h3>
                    <p class="finance-card-subtitle">Pending payments</p>
                </div>
            </div>
            <div class="finance-value">${{ total_receivable|floatformat:0|intcomma }}</div>
            <div class="finance-meta">
                <span>{{ outstanding_rate|floatformat:1 }}% of invoiced</span>
                {% if outstanding_rate > 30 %}
                <span class="finance-percentage negative">
                    <i class="bi bi-exclamation-triangle"></i>
                    High
                </span>
                {% else %}
                <span class="finance-percentage positive">
                    <i class="bi bi-check-circle"></i>
                    Healthy
                </span>
                {% endif %}
            </div>
        </div>

        <div class="finance-card">
            <div class="finance-card-header">
                <div class="finance-icon income">
                    <i class="bi bi-cash-coin"></i>
                </div>
                <div>
                    <h3 class="finance-card-title">Income Received</h3>
                    <p class="finance-card-subtitle">Payments collected</p>
                </div>
            </div>
            <div class="finance-value">${{ total_income|floatformat:0|intcomma }}</div>
            <div class="finance-meta">
                <span>{{ collection_rate|floatformat:1 }}% collected</span>
                {% if collection_rate >= 80 %}
                <span class="finance-percentage positive">
                    <i class="bi bi-arrow-up-short"></i>
                    Excellent
                </span>
                {% elif collection_rate >= 60 %}
                <span class="finance-percentage neutral">
                    <i class="bi bi-dash"></i>
                    Good
                </span>
                {% else %}
                <span class="finance-percentage negative">
                    <i class="bi bi-arrow-down-short"></i>
                    Needs Attention
                </span>
                {% endif %}
            </div>
        </div>

        <div class="finance-card">
            <div class="finance-card-header">
                <div class="finance-icon average">
                    <i class="bi bi-calculator"></i>
                </div>
                <div>
                    <h3 class="finance-card-title">Average Case Value</h3>
                    <p class="finance-card-subtitle">Revenue per case</p>
                </div>
            </div>
            <div class="finance-value">${{ avg_case_value|floatformat:0|intcomma }}</div>
            <div class="finance-meta">
                <span>Across all cases</span>
                <span class="finance-percentage positive">
                    <i class="bi bi-graph-up"></i>
                    Trending
                </span>
            </div>
        </div>
    </div>

    <!-- Operational Summary -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--space-4); margin-bottom: var(--space-8);">
        <div style="background: var(--background); border: 1px solid var(--border); border-radius: var(--rounded-xl); padding: var(--space-4); text-align: center;">
            <div style="font-size: var(--text-2xl); font-weight: 700; color: var(--text-primary);">{{ total_cases|intcomma }}</div>
            <div style="font-size: var(--text-sm); color: var(--text-secondary);">Total Cases</div>
        </div>
        
        <div style="background: var(--background); border: 1px solid var(--border); border-radius: var(--rounded-xl); padding: var(--space-4); text-align: center;">
            <div style="font-size: var(--text-2xl); font-weight: 700; color: var(--success-500);">{{ completed_cases|intcomma }}</div>
            <div style="font-size: var(--text-sm); color: var(--text-secondary);">Completed</div>
        </div>
        
        <div style="background: var(--background); border: 1px solid var(--border); border-radius: var(--rounded-xl); padding: var(--space-4); text-align: center;">
            <div style="font-size: var(--text-2xl); font-weight: 700; color: {% if overdue_cases > 5 %}var(--danger-500){% else %}var(--success-500){% endif %};">{{ overdue_cases|intcomma }}</div>
            <div style="font-size: var(--text-sm); color: var(--text-secondary);">Overdue</div>
        </div>
        
        <div style="background: var(--background); border: 1px solid var(--border); border-radius: var(--rounded-xl); padding: var(--space-4); text-align: center;">
            <div style="font-size: var(--text-2xl); font-weight: 700; color: var(--primary-500);">{{ ready_to_ship|intcomma }}</div>
            <div style="font-size: var(--text-sm); color: var(--text-secondary);">Ready to Ship</div>
        </div>
        
        <div style="background: var(--background); border: 1px solid var(--border); border-radius: var(--rounded-xl); padding: var(--space-4); text-align: center;">
            <div style="font-size: var(--text-2xl); font-weight: 700; color: var(--info-500);">{{ avg_completion_days|floatformat:1 }}</div>
            <div style="font-size: var(--text-sm); color: var(--text-secondary);">Avg Days</div>
        </div>
    </div>

    <!-- Status Breakdown -->
    {% if status_breakdown %}
    <div style="background: var(--background); border: 1px solid var(--border); border-radius: var(--rounded-xl); padding: var(--space-6); margin-bottom: var(--space-8);">
        <h2 style="font-size: var(--text-lg); font-weight: 600; color: var(--text-primary); margin: 0 0 var(--space-4);">Case Status & Financial Breakdown</h2>
        {% for status in status_breakdown %}
        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--space-3) 0; border-bottom: 1px solid var(--border);">
            <div style="font-size: var(--text-sm); color: var(--text-primary); font-weight: 500;">{{ status.status_display }}</div>
            <div style="text-align: right;">
                <div style="font-size: var(--text-sm); color: var(--text-secondary);">{{ status.case_count }} cases</div>
                <div style="font-size: var(--text-base); font-weight: 600; color: var(--text-primary);">${{ status.total_value|floatformat:0|intcomma }}</div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Recent Activities -->
    {% if recent_activities %}
    <div style="background: var(--background); border: 1px solid var(--border); border-radius: var(--rounded-xl); padding: var(--space-6);">
        <h2 style="font-size: var(--text-lg); font-weight: 600; color: var(--text-primary); margin: 0 0 var(--space-4);">Recent Financial Activities</h2>
        {% for activity in recent_activities|slice:":10" %}
        <div style="display: flex; align-items: center; gap: var(--space-3); padding: var(--space-3) 0; border-bottom: 1px solid var(--border);">
            <div style="width: 32px; height: 32px; border-radius: var(--rounded-md); display: flex; align-items: center; justify-content: center; font-size: var(--text-sm); flex-shrink: 0; {% if activity.type == 'invoice' %}background: var(--primary-100); color: var(--primary-600);{% else %}background: var(--success-100); color: var(--success-600);{% endif %}">
                {% if activity.type == 'invoice' %}
                <i class="bi bi-receipt"></i>
                {% elif activity.type == 'payment' %}
                <i class="bi bi-cash-coin"></i>
                {% endif %}
            </div>
            <div style="flex: 1;">
                <div style="font-size: var(--text-sm); color: var(--text-primary); font-weight: 500; margin: 0 0 var(--space-1);">{{ activity.description }}</div>
                <div style="font-size: var(--text-xs); color: var(--text-muted);">{{ activity.date|date:"M d, Y • g:i A" }}</div>
            </div>
            <div style="font-size: var(--text-sm); font-weight: 600; color: var(--text-primary);">${{ activity.amount|floatformat:0|intcomma }}</div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
// Dashboard Functionality
function updateDashboard() {
    const period = document.getElementById('periodSelector').value;
    const url = new URL(window.location);
    url.searchParams.set('range', period);
    window.location.href = url.toString();
}

function refreshDashboard() {
    window.location.reload();
}

console.log('Executive Dashboard loaded successfully');
</script>
{% endblock %}
