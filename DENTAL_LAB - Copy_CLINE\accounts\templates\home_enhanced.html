{% extends "base.html" %}
{% load static humanize mathfilters dashboard_extras %}

{% block title %}Enhanced Dashboard | Dental Case Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
<link rel="stylesheet" href="{% static 'css/dashboard/variables.css' %}">
<link rel="stylesheet" href="{% static 'css/dashboard/layout.css' %}">
<link rel="stylesheet" href="{% static 'css/dashboard/components.css' %}">
<link rel="stylesheet" href="{% static 'css/dashboard/notifications.css' %}">
<style>
/* Enhanced financial metrics styles */
.financial-metric {
    background: linear-gradient(135deg, var(--success-alpha) 0%, transparent 100%);
    border-left: 4px solid var(--success);
}

.dual-metric-card {
    min-height: 140px;
}

.dual-metric-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.primary-metric {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-main);
}

.secondary-metric {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--success);
}

.metric-growth {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    margin-top: var(--space-xs);
}

.currency-selector {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--font-size-sm);
    color: var(--text-main);
}

.financial-summary {
    background: linear-gradient(135deg, var(--primary-alpha) 0%, var(--card-bg) 70%);
    border-radius: var(--border-radius-lg);
    padding: var(--space-lg);
    margin-bottom: var(--space-xl);
    border-left: 5px solid var(--primary);
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-lg);
    margin-top: var(--space-md);
}

.summary-stat {
    text-align: center;
}

.summary-stat-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary);
    display: block;
}

.summary-stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-top: var(--space-xs);
}

.chart-toggle {
    position: absolute;
    top: var(--space-md);
    right: var(--space-lg);
    z-index: 10;
}

.chart-mode-btn {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: var(--transition-fast);
}

.chart-mode-btn.active {
    background: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

.status-value-breakdown {
    margin-top: var(--space-md);
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.status-item:last-child {
    border-bottom: none;
}

.status-info {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.status-values {
    text-align: right;
}

.status-count {
    font-weight: 600;
    color: var(--text-main);
}

.status-value {
    font-size: var(--font-size-sm);
    color: var(--success);
    margin-top: var(--space-xs);
}

@media (max-width: 767.98px) {
    .dual-metric-card {
        min-height: auto;
    }
    
    .summary-stats {
        grid-template-columns: 1fr 1fr;
    }
    
    .chart-toggle {
        position: static;
        margin-bottom: var(--space-md);
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Skip to main content link for accessibility -->
<a href="#main-content" class="skip-link">Skip to main content</a>

<div class="page-container">
    <header class="dashboard-header">
        <div class="dashboard-title-wrapper">
            <h1 class="dashboard-title">Enhanced Dashboard</h1>
            <p class="dashboard-subtitle">Comprehensive Case Volume & Revenue Analytics</p>
        </div>
        <div class="dashboard-actions">
            <div class="input-group-wrapper">
                <i class="bi bi-search" aria-hidden="true"></i>
                <input type="text" 
                       class="input-control search-input" 
                       placeholder="Search cases..." 
                       id="globalSearch" 
                       aria-label="Search cases">
                <button class="btn-action" 
                        title="Search" 
                        aria-label="Execute search">
                    <i class="bi bi-arrow-right" aria-hidden="true"></i>
                </button>
            </div>
            <div class="input-group-wrapper">
                <i class="bi bi-currency-exchange" aria-hidden="true"></i>
                <select class="currency-selector" id="currencySelector" aria-label="Select Currency">
                    <option value="USD">USD ($)</option>
                    <option value="EUR">EUR (€)</option>
                    <option value="GBP">GBP (£)</option>
                    <option value="ALL">ALL (L)</option>
                    <option value="CAD">CAD (C$)</option>
                </select>
            </div>
            <div class="input-group-wrapper">
                <i class="bi bi-calendar3" aria-hidden="true"></i>
                <select class="input-control date-filter" 
                        id="dateRangeFilter" 
                        aria-label="Select Date Range">
                    <option value="7" {% if selected_range == '7' %}selected{% endif %}>Last 7 Days</option>
                    <option value="30" {% if selected_range == '30' %}selected{% endif %}>Last 30 Days</option>
                    <option value="90" {% if selected_range == '90' %}selected{% endif %}>Last 3 Months</option>
                    <option value="180" {% if selected_range == '180' %}selected{% endif %}>Last 6 Months</option>
                    <option value="365" {% if selected_range == '365' %}selected{% endif %}>Last 12 Months</option>
                    <option value="730" {% if selected_range == '730' %}selected{% endif %}>Last 24 Months</option>
                </select>
            </div>
            <div class="action-buttons">
                <button id="darkModeToggle" 
                        class="btn-action" 
                        title="Toggle Theme" 
                        aria-label="Toggle Dark Mode">
                    <i class="bi bi-moon-stars" aria-hidden="true"></i>
                </button>
                <button class="btn-action" 
                        title="Refresh Data" 
                        aria-label="Refresh dashboard data">
                    <i class="bi bi-arrow-clockwise" aria-hidden="true"></i>
                </button>
                <a href="?enhanced=false" class="btn-action" title="Switch to Basic View">
                    <i class="bi bi-speedometer" aria-hidden="true"></i>
                </a>
            </div>
        </div>
    </header>

    <main id="main-content">
        <!-- Error Display -->
        {% if error %}
        <div class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2" aria-hidden="true"></i> 
            {{ error }} Please check system logs or contact support.
            {% if debug_info and user.is_staff %}
            <details class="mt-2">
                <summary>Debug Information (Click to expand)</summary>
                <div class="mt-2 p-3 bg-light text-dark rounded">
                    <p><strong>Exception Type:</strong> {{ debug_info.exception_type }}</p>
                    <p><strong>Exception Message:</strong> {{ debug_info.exception_msg }}</p>
                    <p><strong>Traceback Summary:</strong> {{ debug_info.traceback_summary }}</p>
                </div>
            </details>
            {% endif %}
        </div>
        {% endif %}

        <!-- Financial Summary Banner -->
        <section class="financial-summary" aria-label="Financial Overview">
            <h2 style="margin: 0; font-size: var(--font-size-xl); color: var(--text-main);">
                <i class="bi bi-graph-up-arrow" aria-hidden="true"></i>
                Financial Overview - {{ today }}
            </h2>
            <div class="summary-stats">
                <div class="summary-stat">
                    <span class="summary-stat-value" data-metric="total-value">{{ total_value_formatted|default:"$0.00" }}</span>
                    <span class="summary-stat-label">Total Portfolio Value</span>
                </div>
                <div class="summary-stat">
                    <span class="summary-stat-value" data-metric="month-value">{{ month_value_formatted|default:"$0.00" }}</span>
                    <span class="summary-stat-label">This Month Revenue</span>
                </div>
                <div class="summary-stat">
                    <span class="summary-stat-value" data-metric="avg-case-value">{{ avg_case_value_formatted|default:"$0.00" }}</span>
                    <span class="summary-stat-label">Average Case Value</span>
                </div>
                <div class="summary-stat">
                    <span class="summary-stat-value" data-metric="range-value">{{ range_value_formatted|default:"$0.00" }}</span>
                    <span class="summary-stat-label">{{ selected_range }}-Day Revenue</span>
                </div>
            </div>
        </section>

        <!-- Enhanced Metrics Grid -->
        <section class="metrics-grid" aria-label="Key Performance Metrics">
            <!-- Total Cases + Value -->
            <div class="metric-card dual-metric-card accent-primary">
                <div class="metric-icon icon-primary">
                    <i class="bi bi-folder" aria-hidden="true"></i>
                </div>
                <div class="dual-metric-content">
                    <div>
                        <span class="metric-label">Total Cases</span>
                        <div class="primary-metric" data-metric="total-cases">
                            {{ total_cases|default:0|floatformat:0 }}
                        </div>
                        <div class="secondary-metric" data-metric="total-value-formatted">
                            {{ total_value_formatted|default:"$0.00" }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today's Cases + Value -->
            <div class="metric-card dual-metric-card accent-info">
                <div class="metric-icon icon-info">
                    <i class="bi bi-calendar-day" aria-hidden="true"></i>
                </div>
                <div class="dual-metric-content">
                    <div>
                        <span class="metric-label">Today's Performance</span>
                        <div class="primary-metric" data-metric="cases-today">
                            {{ cases_today|default:0|floatformat:0 }} cases
                        </div>
                        <div class="secondary-metric" data-metric="today-value-formatted">
                            {{ today_value_formatted|default:"$0.00" }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- This Week -->
            <div class="metric-card dual-metric-card accent-success">
                <div class="metric-icon icon-success">
                    <i class="bi bi-calendar-week" aria-hidden="true"></i>
                </div>
                <div class="dual-metric-content">
                    <div>
                        <span class="metric-label">This Week</span>
                        <div class="primary-metric" data-metric="cases-week">
                            {{ cases_this_week|default:0|floatformat:0 }} cases
                        </div>
                        <div class="secondary-metric" data-metric="week-value-formatted">
                            {{ week_value_formatted|default:"$0.00" }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- This Month with Growth -->
            <div class="metric-card dual-metric-card accent-warning">
                <div class="metric-icon icon-warning">
                    <i class="bi bi-calendar-month" aria-hidden="true"></i>
                </div>
                <div class="dual-metric-content">
                    <div>
                        <span class="metric-label">This Month</span>
                        <div class="primary-metric" data-metric="cases-month">
                            {{ cases_this_month|default:0|floatformat:0 }} cases
                        </div>
                        <div class="secondary-metric" data-metric="month-value-formatted">
                            {{ month_value_formatted|default:"$0.00" }}
                        </div>
                        <div class="metric-growth">
                            {% if month_growth > 0 %}
                                <span class="trend-up">
                                    <i class="bi bi-arrow-up" aria-hidden="true"></i>{{ month_growth }}%
                                </span>
                                volume
                            {% elif month_growth < 0 %}
                                <span class="trend-down">
                                    <i class="bi bi-arrow-down" aria-hidden="true"></i>{{ month_growth|floatformat:1 }}%
                                </span>
                                volume
                            {% endif %}
                            {% if month_value_growth %}
                                <span class="mx-1">|</span>
                                {% if month_value_growth > 0 %}
                                    <span class="trend-up">
                                        <i class="bi bi-arrow-up" aria-hidden="true"></i>{{ month_value_growth|floatformat:1 }}%
                                    </span>
                                    revenue
                                {% elif month_value_growth < 0 %}
                                    <span class="trend-down">
                                        <i class="bi bi-arrow-down" aria-hidden="true"></i>{{ month_value_growth|floatformat:1 }}%
                                    </span>
                                    revenue
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Overdue Cases -->
            <div class="metric-card dual-metric-card accent-danger">
                <div class="metric-icon icon-danger">
                    <i class="bi bi-exclamation-triangle" aria-hidden="true"></i>
                </div>
                <div class="dual-metric-content">
                    <div>
                        <span class="metric-label">Overdue Cases</span>
                        <div class="primary-metric" data-metric="overdue-cases">
                            {{ overdue_cases|default:0|floatformat:0 }} cases
                        </div>
                        <div class="secondary-metric" data-metric="overdue-value-formatted">
                            {{ overdue_value_formatted|default:"$0.00" }} at risk
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ready to Ship -->
            <div class="metric-card dual-metric-card accent-purple">
                <div class="metric-icon icon-purple">
                    <i class="bi bi-box-seam" aria-hidden="true"></i>
                </div>
                <div class="dual-metric-content">
                    <div>
                        <span class="metric-label">Ready to Ship</span>
                        <div class="primary-metric" data-metric="ready-ship">
                            {{ ready_to_ship|default:0|floatformat:0 }} cases
                        </div>
                        <div class="secondary-metric" data-metric="ready-ship-value-formatted">
                            {{ ready_to_ship_value_formatted|default:"$0.00" }} ready
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Ready to Ship Banner -->
        {% if ready_to_ship_count|default:0 > 0 %}
        <section class="ready-to-ship-banner" aria-label="Ready to Ship Alert">
            <div class="banner-content">
                <div class="banner-icon" aria-hidden="true">
                    <i class="bi bi-box-seam"></i>
                </div>
                <div class="banner-text">
                    <h3>Ready to Ship Cases</h3>
                    <p>You have <strong>{{ ready_to_ship_count }} {{ ready_to_ship_count|pluralize:"case,cases" }}</strong> 
                       worth <strong>{{ ready_to_ship_value_formatted }}</strong> ready for shipment.</p>
                </div>
            </div>
            <div class="banner-action">
                <a href="{% url 'case:case_list' %}?status=ready_to_ship" class="btn-ship">
                    Process Shipments <i class="bi bi-arrow-right" aria-hidden="true"></i>
                </a>
            </div>
        </section>
        {% endif %}

        <!-- Enhanced Charts Section -->
        <section class="charts-section">
            <!-- Enhanced Trend Chart with Dual Axis -->
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="card" style="position: relative;">
                        <div class="chart-toggle">
                            <button class="chart-mode-btn active" data-mode="volume" aria-label="Show Volume Chart">Volume</button>
                            <button class="chart-mode-btn" data-mode="revenue" aria-label="Show Revenue Chart">Revenue</button>
                            <button class="chart-mode-btn" data-mode="dual" aria-label="Show Dual Axis Chart">Both</button>
                        </div>
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-bar-chart" aria-hidden="true"></i>
                                <span id="chartTitle">Case Volume by Period</span>
                            </h5>
                            <div class="tab-controls" role="tablist">
                                <button class="tab-control active" 
                                        id="trend30DaysBtn" 
                                        role="tab"
                                        aria-pressed="true"
                                        aria-controls="enhancedTrendChart">
                                    Last 30 Days
                                </button>
                                <button class="tab-control" 
                                        id="trend12MonthsBtn" 
                                        role="tab"
                                        aria-pressed="false"
                                        aria-controls="enhancedTrendChart">
                                    Last 12 Months
                                </button>
                                <button class="tab-control" 
                                        id="trend24MonthsBtn" 
                                        role="tab"
                                        aria-pressed="false"
                                        aria-controls="enhancedTrendChart">
                                    Last 24 Months
                                </button>
                                <button class="tab-control" 
                                        id="trendAllTimeBtn" 
                                        role="tab"
                                        aria-pressed="false"
                                        aria-controls="enhancedTrendChart">
                                    All Time
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="enhancedTrendChart" 
                                        role="img" 
                                        aria-label="Enhanced trend chart showing cases and revenue over time"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Breakdown with Financial Data -->
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-pie-chart" aria-hidden="true"></i>
                                Cases by Status - Volume & Revenue
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if status_data %}
                            <div class="status-value-breakdown">
                                {% for status in status_data %}
                                <div class="status-item">
                                    <div class="status-info">
                                        <span class="status-badge status-{{ status.status }}">
                                            {{ status.label }}
                                        </span>
                                    </div>
                                    <div class="status-values">
                                        <div class="status-count">{{ status.count }} cases</div>
                                        <div class="status-value">{{ status.value_formatted }}</div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="empty-state text-center">
                                <i class="bi bi-bar-chart-line" aria-hidden="true"></i>
                                <p class="empty-state-text">No status data available</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Financial Performance Chart -->
                <div class="col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-currency-dollar" aria-hidden="true"></i>
                                Revenue Distribution
                            </h5>
                        </div>
                        <div class="card-body d-flex align-items-center justify-content-center">
                            <div class="chart-container chart-container-doughnut">
                                {% if status_data %}
                                    <canvas id="revenueChart"
                                            role="img"
                                            aria-label="Revenue distribution by status"></canvas>
                                {% else %}
                                    <div class="empty-state text-center">
                                        <i class="bi bi-currency-dollar" aria-hidden="true"></i>
                                        <p class="empty-state-text">No revenue data available</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Data Tables Section (Same as original) -->
        <section class="data-section">
            <div class="row">
                <!-- Recent Cases -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-clock-history" aria-hidden="true"></i>
                                Recent Cases
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            {% if recent_cases %}
                            <div class="table-responsive">
                                <table class="data-table" role="table">
                                    <thead>
                                        <tr>
                                            <th scope="col">Case #</th>
                                            <th scope="col">Patient</th>
                                            <th scope="col">Dentist</th>
                                            <th scope="col">Status</th>
                                            <th scope="col">Received</th>
                                            <th scope="col">Value</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for case in recent_cases %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'case:case_detail' case.case_number %}" 
                                                   class="case-number">
                                                    #{{ case.case_number }}
                                                </a>
                                            </td>
                                            <td>
                                                {% if case.patient %}
                                                    {{ case.patient.get_full_name }}
                                                {% else %}
                                                    <span class="text-muted">No patient assigned</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="dentist-info">
                                                    <div class="avatar avatar-{{ case.dentist.first_name.0|upper }}">
                                                        {{ case.dentist.first_name.0|upper }}{{ case.dentist.last_name.0|upper }}
                                                    </div>
                                                    <div class="dentist-details">
                                                        <div class="dentist-name">{{ case.dentist.get_full_name }}</div>
                                                        <div class="dentist-clinic">{{ case.dentist.clinic_name|default:"Clinic" }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="status-badge status-{{ case.status }}">
                                                    {{ case.get_status_display }}
                                                </span>
                                            </td>
                                            <td>
                                                {% if case.received_date_time %}
                                                    <time datetime="{{ case.received_date_time|date:'c' }}">
                                                        {{ case.received_date_time|date:"M d, Y" }}
                                                    </time>
                                                {% else %}
                                                    <span class="text-muted">Not set</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if case.cost_estimate %}
                                                    <span class="text-success">${{ case.cost_estimate|floatformat:2 }}</span>
                                                {% else %}
                                                    <span class="text-muted">Not estimated</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="empty-state">
                                <i class="bi bi-inbox" aria-hidden="true"></i>
                                <p class="empty-state-text">No recent cases found</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Upcoming Deadlines -->
                <div class="col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-alarm" aria-hidden="true"></i>
                                Upcoming Deadlines
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            {% if upcoming_deadlines %}
                            <div class="table-responsive">
                                <table class="data-table" role="table">
                                    <thead>
                                        <tr>
                                            <th scope="col">Case #</th>
                                            <th scope="col">Deadline</th>
                                            <th scope="col">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for case in upcoming_deadlines %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'case:case_detail' case.case_number %}" 
                                                   class="case-number">
                                                    #{{ case.case_number }}
                                                </a>
                                            </td>
                                            <td>
                                                {% if case.deadline %}
                                                    <time datetime="{{ case.deadline|date:'c' }}">
                                                        {{ case.deadline|date:"M d" }}
                                                    </time>
                                                {% else %}
                                                    <span class="text-muted">Not set</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="status-badge status-{{ case.status }}">
                                                    {{ case.get_status_display }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="empty-state">
                                <i class="bi bi-check-circle" aria-hidden="true"></i>
                                <p class="empty-state-text">No upcoming deadlines</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Provide enhanced data to JavaScript from Django context
    window.trend30DaysData = {{ trend_30_days_json|safe }};
    window.trend12MonthsData = {{ trend_12_months_json|safe }};
    window.trend24MonthsData = {{ trend_24_months_json|safe }};
    window.trendAllTimeData = {{ trend_all_time_json|safe }};
    window.statusChartData = {{ status_json|safe }};
    window.searchCasesUrl = "{% url 'case:search_cases' %}";
    window.currencyConversionUrl = "{% url 'accounts:currency_conversion' %}";
    window.enhancedDashboard = true;
    window.currentCurrency = "{{ currency|default:'USD' }}";
</script>
<script src="{% static 'js/dashboard/enhanced-dashboard.js' %}"></script>
{% endblock %}
