{% extends "base.html" %}
{% load static humanize mathfilters dashboard_extras %}

{% block title %}Dashboard | Dental Case Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
<link rel="stylesheet" href="{% static 'css/dashboard/variables.css' %}">
<link rel="stylesheet" href="{% static 'css/dashboard/layout.css' %}">
<link rel="stylesheet" href="{% static 'css/dashboard/components.css' %}">
<link rel="stylesheet" href="{% static 'css/dashboard/notifications.css' %}">
{% endblock %}

{% block content %}
<!-- Skip to main content link for accessibility -->
<a href="#main-content" class="skip-link">Skip to main content</a>

<div class="page-container">
    <header class="dashboard-header">
        <div class="dashboard-title-wrapper">
            <h1 class="dashboard-title">Dashboard</h1>
            <p class="dashboard-subtitle">Your Dental Case Management Overview</p>
        </div>
        <div class="dashboard-actions">
            <div class="input-group-wrapper">
                <i class="bi bi-search" aria-hidden="true"></i>
                <input type="text" 
                       class="input-control search-input" 
                       placeholder="Search cases..." 
                       id="globalSearch" 
                       aria-label="Search cases">
                <button class="btn-action" 
                        title="Search" 
                        aria-label="Execute search">
                    <i class="bi bi-arrow-right" aria-hidden="true"></i>
                </button>
            </div>
            <div class="input-group-wrapper">
                <i class="bi bi-calendar3" aria-hidden="true"></i>
                <select class="input-control date-filter" 
                        id="dateRangeFilter" 
                        aria-label="Select Date Range">
                    <option value="7" {% if selected_range == '7' %}selected{% endif %}>Last 7 Days</option>
                    <option value="30" {% if selected_range == '30' %}selected{% endif %}>Last 30 Days</option>
                    <option value="90" {% if selected_range == '90' %}selected{% endif %}>Last 3 Months</option>
                    <option value="180" {% if selected_range == '180' %}selected{% endif %}>Last 6 Months</option>
                    <option value="365" {% if selected_range == '365' %}selected{% endif %}>Last 12 Months</option>
                    <option value="730" {% if selected_range == '730' %}selected{% endif %}>Last 24 Months</option>
                </select>
            </div>
            <div class="action-buttons">
                <button id="darkModeToggle" 
                        class="btn-action" 
                        title="Toggle Theme" 
                        aria-label="Toggle Dark Mode">
                    <i class="bi bi-moon-stars" aria-hidden="true"></i>
                </button>
                <button class="btn-action" 
                        title="Refresh Data" 
                        aria-label="Refresh dashboard data">
                    <i class="bi bi-arrow-clockwise" aria-hidden="true"></i>
                </button>
            </div>
        </div>
    </header>

    <main id="main-content">
        <!-- Error Display -->
        {% if error %}
        <div class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2" aria-hidden="true"></i> 
            {{ error }} Please check system logs or contact support.
            {% if debug_info and user.is_staff %}
            <details class="mt-2">
                <summary>Debug Information (Click to expand)</summary>
                <div class="mt-2 p-3 bg-light text-dark rounded">
                    <p><strong>Exception Type:</strong> {{ debug_info.exception_type }}</p>
                    <p><strong>Exception Message:</strong> {{ debug_info.exception_msg }}</p>
                    <p><strong>Traceback Summary:</strong> {{ debug_info.traceback_summary }}</p>
                </div>
            </details>
            {% endif %}
        </div>
        {% endif %}

        <!-- Key Metrics Grid -->
        <section class="metrics-grid" aria-label="Key Performance Metrics">
            <div class="metric-card accent-primary">
                <div class="metric-icon icon-primary">
                    <i class="bi bi-folder" aria-hidden="true"></i>
                </div>
                <div class="metric-content">
                    <span class="metric-label">Total Cases</span>
                    <div class="metric-value" data-metric="total-cases">
                        {{ total_cases|default:0|floatformat:0 }}
                    </div>
                    <div class="metric-comparison">All time</div>
                </div>
            </div>

            <div class="metric-card accent-info">
                <div class="metric-icon icon-info">
                    <i class="bi bi-calendar-day" aria-hidden="true"></i>
                </div>
                <div class="metric-content">
                    <span class="metric-label">Cases Today</span>
                    <div class="metric-value" data-metric="cases-today">
                        {{ cases_today|default:0|floatformat:0 }}
                    </div>
                    <div class="metric-comparison">{{ today }}</div>
                </div>
            </div>

            <div class="metric-card accent-success">
                <div class="metric-icon icon-success">
                    <i class="bi bi-calendar-week" aria-hidden="true"></i>
                </div>
                <div class="metric-content">
                    <span class="metric-label">This Week</span>
                    <div class="metric-value" data-metric="cases-week">
                        {{ cases_this_week|default:0|floatformat:0 }}
                    </div>
                    <div class="metric-comparison">Since Monday</div>
                </div>
            </div>

            <div class="metric-card accent-warning">
                <div class="metric-icon icon-warning">
                    <i class="bi bi-calendar-month" aria-hidden="true"></i>
                </div>
                <div class="metric-content">
                    <span class="metric-label">This Month</span>
                    <div class="metric-value" data-metric="cases-month">
                        {{ cases_this_month|default:0|floatformat:0 }}
                    </div>
                    <div class="metric-comparison">
                        {% if month_growth > 0 %}
                            <span class="trend-up"><i class="bi bi-arrow-up" aria-hidden="true"></i>{{ month_growth }}%</span> vs last month
                        {% elif month_growth < 0 %}
                            <span class="trend-down"><i class="bi bi-arrow-down" aria-hidden="true"></i>{{ month_growth|floatformat:1 }}%</span> vs last month
                        {% else %}
                            <span class="trend-neutral">No change vs last month</span>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="metric-card accent-danger">
                <div class="metric-icon icon-danger">
                    <i class="bi bi-exclamation-triangle" aria-hidden="true"></i>
                </div>
                <div class="metric-content">
                    <span class="metric-label">Overdue Cases</span>
                    <div class="metric-value" data-metric="overdue-cases">
                        {{ overdue_cases|default:0|floatformat:0 }}
                    </div>
                    <div class="metric-comparison">Require attention</div>
                </div>
            </div>

            <div class="metric-card accent-purple">
                <div class="metric-icon icon-purple">
                    <i class="bi bi-box-seam" aria-hidden="true"></i>
                </div>
                <div class="metric-content">
                    <span class="metric-label">Ready to Ship</span>
                    <div class="metric-value" data-metric="ready-ship">
                        {{ ready_to_ship|default:0|floatformat:0 }}
                    </div>
                    <div class="metric-comparison">Awaiting shipment</div>
                </div>
            </div>
        </section>

        <!-- Ready to Ship Banner -->
        {% if ready_to_ship_count|default:0 > 0 %}
        <section class="ready-to-ship-banner" aria-label="Ready to Ship Alert">
            <div class="banner-content">
                <div class="banner-icon" aria-hidden="true">
                    <i class="bi bi-box-seam"></i>
                </div>
                <div class="banner-text">
                    <h3>Ready to Ship Cases</h3>
                    <p>You have <strong>{{ ready_to_ship_count }} {{ ready_to_ship_count|pluralize:"case,cases" }}</strong> ready for shipment. Process them for timely delivery.</p>
                </div>
            </div>
            <div class="banner-action">
                <a href="{% url 'case:case_list' %}?status=ready_to_ship" class="btn-ship">
                    Process Shipments <i class="bi bi-arrow-right" aria-hidden="true"></i>
                </a>
            </div>
        </section>
        {% endif %}

        <!-- Main Charts Section -->
        <section class="charts-section">
            <!-- Trend Chart -->
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-bar-chart" aria-hidden="true"></i>
                                Case Volume by Period
                            </h5>
                            <div class="tab-controls" role="tablist">
                                <button class="tab-control active" 
                                        id="trend30DaysBtn" 
                                        role="tab"
                                        aria-pressed="true"
                                        aria-controls="trendChart">
                                    Last 30 Days
                                </button>
                                <button class="tab-control" 
                                        id="trend12MonthsBtn" 
                                        role="tab"
                                        aria-pressed="false"
                                        aria-controls="trendChart">
                                    Last 12 Months
                                </button>
                                <button class="tab-control" 
                                        id="trend24MonthsBtn" 
                                        role="tab"
                                        aria-pressed="false"
                                        aria-controls="trendChart">
                                    Last 24 Months
                                </button>
                                <button class="tab-control" 
                                        id="trendAllTimeBtn" 
                                        role="tab"
                                        aria-pressed="false"
                                        aria-controls="trendChart">
                                    All Time
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="trendChart" 
                                        role="img" 
                                        aria-label="Case volume trend chart showing cases over time"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Breakdown Charts -->
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-pie-chart" aria-hidden="true"></i>
                                Cases by Status
                            </h5>
                        </div>
                        <div class="card-body d-flex align-items-center justify-content-center">
                            <div class="chart-container chart-container-doughnut">
                                {% if status_chart_data %}
                                    <canvas id="statusChart"
                                            role="img"
                                            aria-label="Distribution of cases by status"></canvas>
                                {% else %}
                                    <div class="empty-state text-center">
                                        <i class="bi bi-bar-chart-line" aria-hidden="true"></i>
                                        <p class="empty-state-text">No status data available</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-flag" aria-hidden="true"></i>
                                Cases by Priority
                            </h5>
                        </div>
                        <div class="card-body d-flex align-items-center justify-content-center">
                            <div class="chart-container chart-container-doughnut">
                                {% if priority_chart_data %}
                                    <canvas id="priorityChart"
                                            role="img"
                                            aria-label="Distribution of cases by priority level"></canvas>
                                {% else %}
                                    <div class="empty-state text-center">
                                        <i class="bi bi-flag" aria-hidden="true"></i>
                                        <p class="empty-state-text">No priority data available</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-building" aria-hidden="true"></i>
                                Cases by Department
                            </h5>
                        </div>
                        <div class="card-body d-flex align-items-center justify-content-center">
                            <div class="chart-container chart-container-doughnut">
                                {% if department_chart_data %}
                                    <canvas id="departmentChart"
                                            role="img"
                                            aria-label="Distribution of cases by department"></canvas>
                                {% else %}
                                    <div class="empty-state text-center">
                                        <i class="bi bi-building" aria-hidden="true"></i>
                                        <p class="empty-state-text">No department data available</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Data Tables Section -->
        <section class="data-section">
            <div class="row">
                <!-- Recent Cases -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-clock-history" aria-hidden="true"></i>
                                Recent Cases
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            {% if recent_cases %}
                            <div class="table-responsive">
                                <table class="data-table" role="table">
                                    <thead>
                                        <tr>
                                            <th scope="col">Case #</th>
                                            <th scope="col">Patient</th>
                                            <th scope="col">Dentist</th>
                                            <th scope="col">Status</th>
                                            <th scope="col">Received</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for case in recent_cases %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'case:case_detail' case.case_number %}" 
                                                   class="case-number">
                                                    #{{ case.case_number }}
                                                </a>
                                            </td>
                                            <td>
                                                {% if case.patient %}
                                                    {{ case.patient.get_full_name }}
                                                {% else %}
                                                    <span class="text-muted">No patient assigned</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="dentist-info">
                                                    <div class="avatar avatar-{{ case.dentist.first_name.0|upper }}">
                                                        {{ case.dentist.first_name.0|upper }}{{ case.dentist.last_name.0|upper }}
                                                    </div>
                                                    <div class="dentist-details">
                                                        <div class="dentist-name">{{ case.dentist.get_full_name }}</div>
                                                        <div class="dentist-clinic">{{ case.dentist.clinic_name|default:"Clinic" }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="status-badge status-{{ case.status }}">
                                                    {{ case.get_status_display }}
                                                </span>
                                            </td>
                                            <td>
                                                {% if case.received_date_time %}
                                                    <time datetime="{{ case.received_date_time|date:'c' }}">
                                                        {{ case.received_date_time|date:"M d, Y" }}
                                                    </time>
                                                {% else %}
                                                    <span class="text-muted">Not set</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="empty-state">
                                <i class="bi bi-inbox" aria-hidden="true"></i>
                                <p class="empty-state-text">No recent cases found</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Upcoming Deadlines -->
                <div class="col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-alarm" aria-hidden="true"></i>
                                Upcoming Deadlines
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            {% if upcoming_deadlines %}
                            <div class="table-responsive">
                                <table class="data-table" role="table">
                                    <thead>
                                        <tr>
                                            <th scope="col">Case #</th>
                                            <th scope="col">Deadline</th>
                                            <th scope="col">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for case in upcoming_deadlines %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'case:case_detail' case.case_number %}" 
                                                   class="case-number">
                                                    #{{ case.case_number }}
                                                </a>
                                            </td>
                                            <td>
                                                {% if case.deadline %}
                                                    <time datetime="{{ case.deadline|date:'c' }}">
                                                        {{ case.deadline|date:"M d" }}
                                                    </time>
                                                {% else %}
                                                    <span class="text-muted">Not set</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="status-badge status-{{ case.status }}">
                                                    {{ case.get_status_display }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="empty-state">
                                <i class="bi bi-check-circle" aria-hidden="true"></i>
                                <p class="empty-state-text">No upcoming deadlines</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Provide data to JavaScript from Django context
    window.trend30DaysData = {{ trend_30_days_json|safe }};
    window.trend12MonthsData = {{ trend_12_months_json|safe }};
    window.trend24MonthsData = {{ trend_24_months_json|safe }};
    window.trendAllTimeData = {{ trend_all_time_json|safe }};
    window.statusChartData = {{ status_chart_data|safe }};
    window.priorityChartData = {{ priority_chart_data|safe }};
    window.departmentChartData = {{ department_chart_data|safe }};
    window.searchCasesUrl = "{% url 'case:search_cases' %}";
</script>
<script src="{% static 'js/dashboard/dashboard.js' %}"></script>
{% endblock %}
