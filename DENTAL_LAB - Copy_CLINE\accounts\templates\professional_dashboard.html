{% extends "base.html" %}
{% load static humanize mathfilters dashboard_extras %}

{% block title %}Executive Dashboard | Dental Case Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
<link rel="stylesheet" href="{% static 'css/dashboard/variables.css' %}">
<style>
/* Professional Executive Dashboard Styles */
:root {
    --executive-primary: #1a365d;
    --executive-secondary: #2d5a87;
    --executive-accent: #3182ce;
    --executive-success: #059669;
    --executive-warning: #d97706;
    --executive-danger: #dc2626;
    
    --executive-bg: #f8fafc;
    --executive-card: #ffffff;
    --executive-border: #e2e8f0;
    --executive-text-primary: #1a202c;
    --executive-text-secondary: #4a5568;
    --executive-text-muted: #718096;
    
    --executive-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --executive-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 2.5rem;
    --space-3xl: 3rem;
}

[data-theme="dark"] {
    --executive-bg: #0f172a;
    --executive-card: #1e293b;
    --executive-border: #334155;
    --executive-text-primary: #f1f5f9;
    --executive-text-secondary: #cbd5e1;
    --executive-text-muted: #94a3b8;
}

body {
    background-color: var(--executive-bg);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    color: var(--executive-text-primary);
    line-height: 1.5;
}

.executive-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-lg) var(--space-xl);
}

/* Executive Header */
.executive-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-2xl);
    padding-bottom: var(--space-lg);
    border-bottom: 2px solid var(--executive-border);
}

.executive-title {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--executive-primary);
    letter-spacing: -0.025em;
}

.executive-subtitle {
    color: var(--executive-text-secondary);
    margin-top: var(--space-xs);
    font-size: 1.125rem;
    font-weight: 500;
}

.executive-controls {
    display: flex;
    gap: var(--space-md);
    align-items: center;
}

.executive-select {
    background: var(--executive-card);
    border: 1px solid var(--executive-border);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: var(--executive-text-primary);
    min-width: 140px;
}

.executive-btn {
    background: var(--executive-primary);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.executive-btn:hover {
    background: var(--executive-secondary);
    transform: translateY(-1px);
}

/* Executive Summary Cards */
.executive-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-2xl);
}

.executive-card {
    background: var(--executive-card);
    border: 1px solid var(--executive-border);
    border-radius: 16px;
    padding: var(--space-xl);
    box-shadow: var(--executive-shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.executive-card:hover {
    box-shadow: var(--executive-shadow-lg);
    transform: translateY(-2px);
}

.executive-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--card-accent, var(--executive-accent));
}

.executive-card.card-primary { --card-accent: var(--executive-primary); }
.executive-card.card-success { --card-accent: var(--executive-success); }
.executive-card.card-warning { --card-accent: var(--executive-warning); }
.executive-card.card-danger { --card-accent: var(--executive-danger); }

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-lg);
}

.card-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--executive-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0;
}

.card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--card-accent, var(--executive-accent));
    color: white;
    font-size: 1.25rem;
}

.card-value {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--executive-text-primary);
    margin-bottom: var(--space-sm);
    line-height: 1.1;
}

.card-subtitle {
    font-size: 0.875rem;
    color: var(--executive-text-muted);
    margin-bottom: var(--space-md);
}

.card-growth {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    font-size: 0.875rem;
    font-weight: 500;
}

.growth-positive { color: var(--executive-success); }
.growth-negative { color: var(--executive-danger); }
.growth-neutral { color: var(--executive-text-muted); }

/* Financial Performance Section */
.financial-performance {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-xl);
    margin-bottom: var(--space-2xl);
}

.performance-card {
    background: var(--executive-card);
    border: 1px solid var(--executive-border);
    border-radius: 16px;
    padding: var(--space-xl);
    box-shadow: var(--executive-shadow);
}

.performance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-md);
    border-bottom: 1px solid var(--executive-border);
}

.performance-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--executive-text-primary);
    margin: 0;
}

.chart-container {
    position: relative;
    height: 320px;
    width: 100%;
}

/* Status Analysis */
.status-analysis {
    background: var(--executive-card);
    border: 1px solid var(--executive-border);
    border-radius: 16px;
    padding: var(--space-xl);
    box-shadow: var(--executive-shadow);
    margin-bottom: var(--space-xl);
}

.analysis-header {
    margin-bottom: var(--space-lg);
}

.analysis-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--executive-text-primary);
    margin: 0 0 var(--space-xs) 0;
}

.analysis-subtitle {
    color: var(--executive-text-secondary);
    font-size: 0.875rem;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-lg);
}

.status-item {
    padding: var(--space-lg);
    border: 1px solid var(--executive-border);
    border-radius: 12px;
    transition: all 0.2s ease;
}

.status-item:hover {
    border-color: var(--executive-accent);
    box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.1);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-md);
}

.status-pending_acceptance { background: #fef3c7; color: #92400e; }
.status-in_progress { background: #dbeafe; color: #1e40af; }
.status-quality_check { background: #f3e8ff; color: #7c2d12; }
.status-ready_to_ship { background: #d1fae5; color: #065f46; }
.status-shipped { background: #d1fae5; color: #065f46; }
.status-delivered { background: #d1fae5; color: #065f46; }

.status-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-md);
}

.metric-item {
    text-align: center;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--executive-text-primary);
    margin-bottom: var(--space-xs);
}

.metric-label {
    font-size: 0.75rem;
    color: var(--executive-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Operational Alerts */
.operational-alerts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.alert-card {
    background: var(--executive-card);
    border: 1px solid var(--executive-border);
    border-radius: 12px;
    padding: var(--space-lg);
    box-shadow: var(--executive-shadow);
    border-left: 4px solid var(--alert-color, var(--executive-accent));
}

.alert-card.alert-danger { --alert-color: var(--executive-danger); }
.alert-card.alert-warning { --alert-color: var(--executive-warning); }
.alert-card.alert-success { --alert-color: var(--executive-success); }

.alert-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--executive-text-secondary);
    margin-bottom: var(--space-sm);
}

.alert-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--alert-color, var(--executive-text-primary));
    margin-bottom: var(--space-xs);
}

.alert-description {
    font-size: 0.875rem;
    color: var(--executive-text-muted);
}

/* Recent Activity */
.recent-activity {
    background: var(--executive-card);
    border: 1px solid var(--executive-border);
    border-radius: 16px;
    padding: var(--space-xl);
    box-shadow: var(--executive-shadow);
}

.activity-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.activity-table th {
    background: var(--executive-bg);
    padding: var(--space-md);
    text-align: left;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--executive-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid var(--executive-border);
}

.activity-table td {
    padding: var(--space-md);
    border-bottom: 1px solid var(--executive-border);
    font-size: 0.875rem;
    color: var(--executive-text-primary);
}

.activity-table tr:hover {
    background: var(--executive-bg);
}

.case-link {
    color: var(--executive-accent);
    text-decoration: none;
    font-weight: 500;
}

.case-link:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .financial-performance {
        grid-template-columns: 1fr;
    }
    
    .executive-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-lg);
    }
    
    .executive-controls {
        width: 100%;
        justify-content: flex-end;
    }
}

@media (max-width: 768px) {
    .executive-container {
        padding: var(--space-md);
    }
    
    .executive-summary {
        grid-template-columns: 1fr;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .operational-alerts {
        grid-template-columns: 1fr;
    }
    
    .executive-title {
        font-size: 1.5rem;
    }
    
    .card-value {
        font-size: 1.875rem;
    }
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, 
        var(--executive-border) 25%, 
        var(--executive-bg) 50%, 
        var(--executive-border) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Print Styles */
@media print {
    .executive-controls {
        display: none;
    }
    
    .executive-card,
    .performance-card,
    .status-analysis,
    .recent-activity {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="executive-container">
    <!-- Executive Header -->
    <header class="executive-header">
        <div>
            <h1 class="executive-title">Executive Dashboard</h1>
            <p class="executive-subtitle">Comprehensive Financial & Operational Analytics</p>
        </div>
        <div class="executive-controls">
            <select class="executive-select" id="dateRangeFilter" aria-label="Select Analysis Period">
                <option value="7" {% if selected_range == '7' %}selected{% endif %}>Last 7 Days</option>
                <option value="30" {% if selected_range == '30' %}selected{% endif %}>Last 30 Days</option>
                <option value="90" {% if selected_range == '90' %}selected{% endif %}>Last Quarter</option>
                <option value="180" {% if selected_range == '180' %}selected{% endif %}>Last 6 Months</option>
                <option value="365" {% if selected_range == '365' %}selected{% endif %}>Last 12 Months</option>
            </select>
            <button class="executive-btn" onclick="window.location.reload();">
                <i class="bi bi-arrow-clockwise"></i>
                Refresh
            </button>
            <button class="executive-btn" onclick="window.print();">
                <i class="bi bi-printer"></i>
                Export
            </button>
        </div>
    </header>

    <!-- Error Display -->
    {% if error %}
    <div class="alert-card alert-danger" style="margin-bottom: 2rem;">
        <div class="alert-title">System Alert</div>
        <div class="alert-description">{{ error }}</div>
    </div>
    {% endif %}

    <!-- Executive Summary Cards -->
    <section class="executive-summary">
        <!-- Total Portfolio -->
        <div class="executive-card card-primary">
            <div class="card-header">
                <div>
                    <h3 class="card-title">Total Portfolio</h3>
                </div>
                <div class="card-icon">
                    <i class="bi bi-briefcase"></i>
                </div>
            </div>
            <div class="card-value">{{ total_cases|default:0|floatformat:0 }}</div>
            <div class="card-subtitle">Active Cases</div>
            <div class="card-growth">
                <span class="growth-neutral">{{ active_cases|default:0 }} in production</span>
            </div>
        </div>

        <!-- Month-to-Date Invoiced -->
        <div class="executive-card card-success">
            <div class="card-header">
                <div>
                    <h3 class="card-title">MTD Invoiced</h3>
                </div>
                <div class="card-icon">
                    <i class="bi bi-receipt"></i>
                </div>
            </div>
            <div class="card-value">{{ mtd_invoiced_formatted|default:"$0.00" }}</div>
            <div class="card-subtitle">Total Billed This Month</div>
            {% if mtd_invoiced_growth %}
            <div class="card-growth">
                {% if mtd_invoiced_growth > 0 %}
                    <i class="bi bi-arrow-up growth-positive"></i>
                    <span class="growth-positive">+{{ mtd_invoiced_growth }}% vs last month</span>
                {% elif mtd_invoiced_growth < 0 %}
                    <i class="bi bi-arrow-down growth-negative"></i>
                    <span class="growth-negative">{{ mtd_invoiced_growth }}% vs last month</span>
                {% else %}
                    <span class="growth-neutral">No change vs last month</span>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <!-- Month-to-Date Income -->
        <div class="executive-card card-success">
            <div class="card-header">
                <div>
                    <h3 class="card-title">MTD Income</h3>
                </div>
                <div class="card-icon">
                    <i class="bi bi-bank"></i>
                </div>
            </div>
            <div class="card-value">{{ mtd_income_formatted|default:"$0.00" }}</div>
            <div class="card-subtitle">Cash Received This Month</div>
            {% if income_growth %}
            <div class="card-growth">
                {% if income_growth > 0 %}
                    <i class="bi bi-arrow-up growth-positive"></i>
                    <span class="growth-positive">+{{ income_growth }}% vs last month</span>
                {% elif income_growth < 0 %}
                    <i class="bi bi-arrow-down growth-negative"></i>
                    <span class="growth-negative">{{ income_growth }}% vs last month</span>
                {% else %}
                    <span class="growth-neutral">No change vs last month</span>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <!-- Outstanding Receivables -->
        <div class="executive-card card-warning">
            <div class="card-header">
                <div>
                    <h3 class="card-title">Receivables</h3>
                </div>
                <div class="card-icon">
                    <i class="bi bi-hourglass-split"></i>
                </div>
            </div>
            <div class="card-value">{{ mtd_receivable_formatted|default:"$0.00" }}</div>
            <div class="card-subtitle">Outstanding Amount</div>
            <div class="card-growth">
                <span class="growth-neutral">{{ collection_rate|default:0 }}% collection rate</span>
            </div>
        </div>

        <!-- Average Case Value -->
        <div class="executive-card card-primary">
            <div class="card-header">
                <div>
                    <h3 class="card-title">Avg Case Value</h3>
                </div>
                <div class="card-icon">
                    <i class="bi bi-calculator"></i>
                </div>
            </div>
            <div class="card-value">{{ avg_case_value_formatted|default:"$0.00" }}</div>
            <div class="card-subtitle">Per Case Invoice Average</div>
            {% if days_sales_outstanding %}
            <div class="card-growth">
                <span class="growth-neutral">{{ days_sales_outstanding }} days DSO</span>
            </div>
            {% endif %}
        </div>

        <!-- Year-to-Date Performance -->
        <div class="executive-card card-primary">
            <div class="card-header">
                <div>
                    <h3 class="card-title">YTD Performance</h3>
                </div>
                <div class="card-icon">
                    <i class="bi bi-graph-up"></i>
                </div>
            </div>
            <div class="card-value">{{ ytd_invoiced_formatted|default:"$0.00" }}</div>
            <div class="card-subtitle">Year-to-Date Invoiced</div>
            <div class="card-growth">
                <span class="growth-neutral">{{ ytd_income_formatted|default:"$0.00" }} collected</span>
            </div>
        </div>
    </section>

    <!-- Financial Performance Charts -->
    <section class="financial-performance">
        <div class="performance-card">
            <div class="performance-header">
                <h3 class="performance-title">Financial Trends</h3>
                <div class="chart-controls">
                    <button class="executive-btn" id="chartToggle" data-view="invoiced">
                        <i class="bi bi-bar-chart"></i>
                        Invoiced
                    </button>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="financialTrendChart"></canvas>
            </div>
        </div>

        <div class="performance-card">
            <div class="performance-header">
                <h3 class="performance-title">Cash Flow</h3>
            </div>
            <div class="chart-container">
                <canvas id="cashFlowChart"></canvas>
            </div>
        </div>
    </section>

    <!-- Operational Alerts -->
    <section class="operational-alerts">
        <div class="alert-card alert-danger">
            <div class="alert-title">Overdue Cases</div>
            <div class="alert-value">{{ overdue_cases|default:0 }}</div>
            <div class="alert-description">{{ overdue_value_formatted|default:"$0.00" }} at risk</div>
        </div>

        <div class="alert-card alert-success">
            <div class="alert-title">Ready to Ship</div>
            <div class="alert-value">{{ ready_to_ship|default:0 }}</div>
            <div class="alert-description">Cases awaiting delivery</div>
        </div>

        <div class="alert-card alert-warning">
            <div class="alert-title">High-Value Cases</div>
            <div class="alert-value">{{ high_value_cases|default:0 }}</div>
            <div class="alert-description">{{ high_value_amount_formatted|default:"$0.00" }} value</div>
        </div>

        <div class="alert-card">
            <div class="alert-title">In Production</div>
            <div class="alert-value">{{ in_production|default:0 }}</div>
            <div class="alert-description">Currently being worked</div>
        </div>
    </section>

    <!-- Status Analysis -->
    <section class="status-analysis">
        <div class="analysis-header">
            <h3 class="analysis-title">Status Analysis</h3>
            <p class="analysis-subtitle">Financial breakdown by case status</p>
        </div>
        
        <div class="status-grid">
            {% for status in status_analysis %}
            <div class="status-item">
                <div class="status-badge status-{{ status.status }}">
                    {{ status.label }}
                </div>
                <div class="status-metrics">
                    <div class="metric-item">
                        <div class="metric-value">{{ status.case_count }}</div>
                        <div class="metric-label">Cases</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">{{ status.invoiced_formatted }}</div>
                        <div class="metric-label">Invoiced</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">{{ status.income_formatted }}</div>
                        <div class="metric-label">Income</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">{{ status.receivable_formatted }}</div>
                        <div class="metric-label">Receivable</div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="status-item">
                <div class="metric-value">No data available</div>
                <div class="metric-label">Please check data sources</div>
            </div>
            {% endfor %}
        </div>
    </section>

    <!-- Recent Activity -->
    <section class="recent-activity">
        <div class="analysis-header">
            <h3 class="analysis-title">Recent Cases</h3>
            <p class="analysis-subtitle">Latest case activity and updates</p>
        </div>
        
        {% if recent_cases %}
        <table class="activity-table">
            <thead>
                <tr>
                    <th>Case #</th>
                    <th>Patient</th>
                    <th>Dentist</th>
                    <th>Status</th>
                    <th>Received</th>
                    <th>Value</th>
                </tr>
            </thead>
            <tbody>
                {% for case in recent_cases %}
                <tr>
                    <td>
                        <a href="{% url 'case:case_detail' case.case_number %}" class="case-link">
                            #{{ case.case_number }}
                        </a>
                    </td>
                    <td>
                        {% if case.patient %}
                            {{ case.patient.get_full_name }}
                        {% else %}
                            <span style="color: var(--executive-text-muted);">Unassigned</span>
                        {% endif %}
                    </td>
                    <td>{{ case.dentist.get_full_name }}</td>
                    <td>
                        <span class="status-badge status-{{ case.status }}">
                            {{ case.get_status_display }}
                        </span>
                    </td>
                    <td>
                        {% if case.received_date_time %}
                            {{ case.received_date_time|date:"M d, Y" }}
                        {% else %}
                            <span style="color: var(--executive-text-muted);">Not set</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if case.cost_estimate %}
                            <strong>${{ case.cost_estimate|floatformat:2 }}</strong>
                        {% else %}
                            <span style="color: var(--executive-text-muted);">TBD</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div style="text-align: center; padding: 2rem; color: var(--executive-text-muted);">
            <i class="bi bi-inbox" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
            No recent cases found for the selected period.
        </div>
        {% endif %}
    </section>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Professional Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts with professional styling
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true,
                    font: {
                        family: 'Inter',
                        size: 12
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(226, 232, 240, 0.3)'
                },
                ticks: {
                    font: {
                        family: 'Inter',
                        size: 11
                    }
                }
            },
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    font: {
                        family: 'Inter',
                        size: 11
                    }
                }
            }
        }
    };

    // Initialize Financial Trend Chart
    const financialCtx = document.getElementById('financialTrendChart');
    if (financialCtx) {
        const trendData = {{ trends_30_days_json|safe }};
        
        new Chart(financialCtx, {
            type: 'line',
            data: {
                labels: trendData.map(item => item.label),
                datasets: [{
                    label: 'Invoiced Amount',
                    data: trendData.map(item => item.invoiced || 0),
                    borderColor: '#3182ce',
                    backgroundColor: 'rgba(49, 130, 206, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: chartOptions
        });
    }

    // Initialize Cash Flow Chart
    const cashFlowCtx = document.getElementById('cashFlowChart');
    if (cashFlowCtx) {
        const trendData = {{ trends_30_days_json|safe }};
        
        new Chart(cashFlowCtx, {
            type: 'bar',
            data: {
                labels: trendData.map(item => item.label),
                datasets: [
                    {
                        label: 'Income',
                        data: trendData.map(item => item.income || 0),
                        backgroundColor: '#059669',
                        borderRadius: 4
                    },
                    {
                        label: 'Receivable',
                        data: trendData.map(item => item.receivable || 0),
                        backgroundColor: '#d97706',
                        borderRadius: 4
                    }
                ]
            },
            options: chartOptions
        });
    }

    // Date range filter
    document.getElementById('dateRangeFilter')?.addEventListener('change', function() {
        const range = this.value;
        const url = new URL(window.location);
        url.searchParams.set('range', range);
        window.location.href = url.toString();
    });

    // Chart toggle functionality
    document.getElementById('chartToggle')?.addEventListener('click', function() {
        const currentView = this.dataset.view;
        const newView = currentView === 'invoiced' ? 'income' : 'invoiced';
        this.dataset.view = newView;
        this.innerHTML = `<i class="bi bi-bar-chart"></i> ${newView.charAt(0).toUpperCase() + newView.slice(1)}`;
        
        // Update chart data based on new view
        // Implementation would update the chart dataset
    });
});
</script>
{% endblock %}
