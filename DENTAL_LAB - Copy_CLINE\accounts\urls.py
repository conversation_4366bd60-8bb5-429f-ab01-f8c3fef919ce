# accounts/urls.py

from django.urls import path
from django.contrib.auth import views as auth_views
from . import views
from .views import (
    LoginView,
    AdminDashboardView, ManagerDashboardView, StaffDashboardView,
    DashboardRouterView, dashboard_router, dashboard_api, department_performance_api,
)
from .views.api import ManagerDashboardFilterApiView # Import the new API view
from .views.dashboard_views import (
    DashboardView, home_function_view, dashboard_metrics_api
)
from .views.enhanced_dashboard_views import (
    EnhancedDashboardView, enhanced_home_view, enhanced_dashboard_api,
    financial_metrics_api, currency_conversion_api
)
from .views.professional_dashboard_views import (
    ProfessionalDashboardView, professional_home_view, professional_dashboard_api,
    financial_summary_api, operational_metrics_api
)
from .views.executive_dashboard_views import (
    ExecutiveDashboardView, executive_home_view, executive_dashboard_api,
    financial_summary_api as exec_financial_api, operational_metrics_api as exec_operational_api
)

app_name = 'accounts'

urlpatterns = [
    # Authentication URLs
    path('register/', views.RegisterView.as_view(), name='register'),
    path('login/', LoginView.as_view(), name='login'),
    path('logout/', views.logout_view, name='logout'),

    # Profile Management URLs
    path('profile/', views.ProfileView.as_view(), name='profile'),
    path('profile/settings/', views.UserSettingsView.as_view(), name='profile_settings'),
    path('profile/password/', views.PasswordManagementView.as_view(), name='change_password'),
    path('profile/image/update/', views.ProfileImageUpdateView.as_view(), name='update_profile_image'),

    # Qualification URLs
    path('qualifications/', views.QualificationListView.as_view(), name='qualifications'),
    path('qualifications/add/', views.QualificationCreateView.as_view(), name='add_qualification'),
    path('qualifications/<int:pk>/delete/', views.QualificationDeleteView.as_view(), name='delete_qualification'),

    path('qualifications/', views.QualificationListView.as_view(), name='qualifications'),
    path('qualifications/create/', views.QualificationCreateView.as_view(), name='qualification_create'),
    # path('qualifications/<int:pk>/update/', views.QualificationUpdateView.as_view(), name='qualification_update'),
    path('qualifications/<int:pk>/delete/', views.QualificationDeleteView.as_view(), name='qualification_delete'),


    # Activity Log
    path('activity-log/', views.ActivityLogView.as_view(), name='activity_log'),

    # Password Reset URLs
    path('password-reset/', views.PasswordResetView.as_view(), name='password_reset'),
    path('password-reset/done/',
        auth_views.PasswordResetDoneView.as_view(
            template_name='accounts/auth/password_reset_done.html'
        ),
        name='password_reset_done'
    ),
    path('password-reset/confirm/<uidb64>/<token>/',
        auth_views.PasswordResetConfirmView.as_view(
            template_name='accounts/auth/password_reset_confirm.html',
            success_url='/accounts/password-reset/complete/'
        ),
        name='password_reset_confirm'
    ),
    path('password-reset/complete/',
        auth_views.PasswordResetCompleteView.as_view(
            template_name='accounts/auth/password_reset_complete.html'
        ),
        name='password_reset_complete'
    ),

    # Email Verification URLs
    path('verify-email/<str:uidb64>/<str:token>/',
        views.EmailVerificationView.as_view(),
        name='verify_email'
    ),
    path('resend-verification/',
        views.ResendVerificationView.as_view(),
        name='resend_verification'
    ),

    # Admin URLs (require superuser)
    path('users/', views.UserManagementView.as_view(), name='user_management'),
    path('users/<int:pk>/', views.UserDetailView.as_view(), name='user_detail'),
    path('users/<int:pk>/edit/', views.UserUpdateView.as_view(), name='user_edit'),
    path('users/<int:pk>/delete/', views.UserDeleteView.as_view(), name='user_delete'),

    # Error Pages
    path('403/', views.custom_403, name='403'),
    path('404/', views.custom_404, name='404'),
    path('500/', views.custom_500, name='500'),


    path('settings/update/', views.UserSettingsUpdateView.as_view(), name='settings_update'),
    path('2fa/setup/', views.TwoFactorSetupView.as_view(), name='setup_2fa'),
    path('activity-log/', views.ActivityLogView.as_view(), name='activity_log'),
    path('activity-log/export/', views.ExportActivityLogView.as_view(), name='export_activity_log'),


    path('system-settings/', views.SystemSettingsView.as_view(), name='system_settings'),

    # Dashboard URLs
    path('dashboards/', DashboardRouterView.as_view(), name='dashboard_router'),
    path('dashboards/admin/', AdminDashboardView.as_view(), name='admin_dashboard'),
    path('dashboards/manager/', ManagerDashboardView.as_view(), name='manager_dashboard'),
    path('dashboards/staff/', StaffDashboardView.as_view(), name='staff_dashboard'),
    path('dashboards/redirect/', dashboard_router, name='dashboard_redirect'),

    # API URLs
    path('dashboards/manager/filter/', ManagerDashboardFilterApiView.as_view(), name='manager_dashboard_filter_api'),

    # Modern Dashboard URLs
    path('modern-dashboard/', views.modern_dashboard, name='modern_dashboard'),
    path('api/dashboard/', dashboard_api, name='dashboard_api'),
    path('api/department-performance/', department_performance_api, name='department_performance_api'),
    
    # Improved Dashboard URLs (New Implementation)
    path('dashboard/improved/', DashboardView.as_view(), name='dashboard_improved'),
    path('dashboard/new/', home_function_view, name='dashboard_new'),
    path('api/dashboard/metrics/', dashboard_metrics_api, name='dashboard_metrics_api'),
    
    # Enhanced Dashboard URLs (Financial Metrics)
    path('dashboard/enhanced/', EnhancedDashboardView.as_view(), name='dashboard_enhanced'),
    path('dashboard/financial/', enhanced_home_view, name='dashboard_financial'),
    path('api/dashboard/enhanced/', enhanced_dashboard_api, name='enhanced_dashboard_api'),
    path('api/dashboard/financial/', financial_metrics_api, name='financial_metrics_api'),
    path('api/currency/convert/', currency_conversion_api, name='currency_conversion'),
    
    # Professional Dashboard URLs (Executive Analytics)
    path('dashboard/professional/', ProfessionalDashboardView.as_view(), name='dashboard_professional'),
    path('dashboard/executive/', professional_home_view, name='dashboard_executive'),
    path('api/dashboard/professional/', professional_dashboard_api, name='professional_dashboard_api'),
    path('api/dashboard/executive/', financial_summary_api, name='financial_summary_api'),
    path('api/dashboard/operational/', operational_metrics_api, name='operational_metrics_api'),
    
    # Executive Dashboard URLs (New - Superior Implementation)
    path('dashboard/exec/', ExecutiveDashboardView.as_view(), name='dashboard_exec'),
    path('dashboard/executive-home/', executive_home_view, name='dashboard_executive_home'),
    path('api/dashboard/exec/', executive_dashboard_api, name='executive_dashboard_api'),
    path('api/dashboard/exec-financial/', exec_financial_api, name='exec_financial_api'),
    path('api/dashboard/exec-operational/', exec_operational_api, name='exec_operational_api'),
]

# Add to your project's main urls.py (LAB/urls.py)
handler403 = 'accounts.views.custom_403'
handler404 = 'accounts.views.custom_404'
handler500 = 'accounts.views.custom_500'
