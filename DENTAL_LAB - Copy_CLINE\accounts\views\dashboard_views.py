"""
Improved Dashboard Views
Clean, maintainable views using service layer architecture
"""

from django.contrib.auth.decorators import login_required
from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.cache import cache_page
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
import logging

from ..services.dashboard_service import DashboardService

logger = logging.getLogger(__name__)


class DashboardView(LoginRequiredMixin, TemplateView):
    """
    Modern class-based dashboard view using service layer
    """
    template_name = 'home_improved.html'  # Use the improved template
    login_url = 'accounts:login'
    
    def get_context_data(self, **kwargs):
        """Get dashboard context data using service layer"""
        context = super().get_context_data(**kwargs)
        
        try:
            # Get date range from request
            date_range_days = self._get_date_range()
            
            # Initialize dashboard service
            dashboard_service = DashboardService(date_range_days)
            
            # Get all dashboard data
            dashboard_data = dashboard_service.get_complete_dashboard_data()
            
            # Add to context
            context.update(dashboard_data)
            context['user'] = self.request.user
            
            logger.info(f"Dashboard loaded successfully for user {self.request.user.id}")
            
        except Exception as e:
            logger.error(f"Dashboard error for user {self.request.user.id}: {e}")
            context.update(self._get_error_context())
        
        return context
    
    def _get_date_range(self) -> int:
        """Get and validate date range from request"""
        try:
            date_range = int(self.request.GET.get('range', '30'))
            # Validate range
            if date_range not in [7, 30, 90, 180, 365, 730]:
                date_range = 30
            return date_range
        except (ValueError, TypeError):
            return 30
    
    def _get_error_context(self) -> dict:
        """Get minimal context for error states"""
        return {
            'error': 'Unable to load dashboard data. Please try again.',
            'total_cases': 0,
            'cases_today': 0,
            'cases_this_week': 0,
            'cases_this_month': 0,
            'month_growth': 0,
            'overdue_cases': 0,
            'ready_to_ship': 0,
            'in_progress': 0,
            'recent_cases': [],
            'upcoming_deadlines': [],
            'selected_range': '30',
        }


@login_required(login_url='accounts:login')
def home_function_view(request):
    """
    Improved function-based home view (alternative to class-based)
    Clean, focused implementation using service layer
    """
    try:
        # Get date range from request
        selected_range = request.GET.get('range', '30')
        try:
            days_in_range = int(selected_range)
            if days_in_range not in [7, 30, 90, 180, 365, 730]:
                days_in_range = 30
                selected_range = '30'
        except ValueError:
            days_in_range = 30
            selected_range = '30'
        
        # Initialize dashboard service
        dashboard_service = DashboardService(days_in_range)
        
        # Get all dashboard data
        context = dashboard_service.get_complete_dashboard_data()
        
        # Add user and meta information
        context.update({
            'user': request.user,
            'selected_range': selected_range,
        })
        
        logger.info(f"Dashboard loaded successfully for user {request.user.id}")
        return render(request, 'home_improved.html', context)
        
    except Exception as e:
        logger.error(f"Dashboard error for user {request.user.id}: {e}")
        
        # Return error context
        error_context = {
            'error': 'Unable to load dashboard data. Please try again.',
            'debug_info': {
                'exception_type': type(e).__name__,
                'exception_msg': str(e),
                'traceback_summary': 'Check server logs for details'
            } if request.user.is_staff else None,
            'total_cases': 0,
            'cases_today': 0,
            'cases_this_week': 0,
            'cases_this_month': 0,
            'month_growth': 0,
            'overdue_cases': 0,
            'ready_to_ship': 0,
            'in_progress': 0,
            'recent_cases': [],
            'upcoming_deadlines': [],
            'selected_range': selected_range,
            'user': request.user,
        }
        
        return render(request, 'home_improved.html', error_context)


@require_http_methods(["GET"])
@login_required
def dashboard_api(request):
    """
    API endpoint for dashboard data (AJAX refresh)
    """
    try:
        date_range_days = int(request.GET.get('range', '30'))
        if date_range_days not in [7, 30, 90, 180, 365, 730]:
            date_range_days = 30
        
        dashboard_service = DashboardService(date_range_days)
        data = dashboard_service.get_complete_dashboard_data()
        
        return JsonResponse({
            'success': True,
            'data': data
        })
        
    except Exception as e:
        logger.error(f"Dashboard API error: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Unable to load dashboard data'
        }, status=500)


@require_http_methods(["GET"])
@login_required
@cache_page(60 * 5)  # Cache for 5 minutes
def dashboard_metrics_api(request):
    """
    Lightweight API endpoint for just metrics (for widgets)
    """
    try:
        date_range_days = int(request.GET.get('range', '30'))
        dashboard_service = DashboardService(date_range_days)
        
        # Get only basic metrics for performance
        basic_metrics = dashboard_service.data_service.get_basic_metrics()
        status_metrics = dashboard_service.data_service.get_status_metrics()
        
        return JsonResponse({
            'success': True,
            'metrics': {
                **basic_metrics,
                **status_metrics
            }
        })
        
    except Exception as e:
        logger.error(f"Dashboard metrics API error: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Unable to load metrics'
        }, status=500)


# Legacy function for backward compatibility
# This replaces the old 800+ line function in main.py
@login_required(login_url='accounts:login')
def home(request):
    """
    Legacy home view - redirects to improved implementation
    Maintains backward compatibility while using new service layer
    """
    return home_function_view(request)
