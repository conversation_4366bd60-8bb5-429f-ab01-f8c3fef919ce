"""
Enhanced Dashboard Views with Financial Metrics
Includes both volume and value tracking with currency support
"""

from django.contrib.auth.decorators import login_required
from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.cache import cache_page
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
import logging

from ..services.enhanced_dashboard_service import EnhancedDashboardService
from ..services.dashboard_service import DashboardService

logger = logging.getLogger(__name__)


class EnhancedDashboardView(LoginRequiredMixin, TemplateView):
    """
    Enhanced dashboard view with financial metrics
    Shows both case volume and revenue data
    """
    template_name = 'home_enhanced.html'
    login_url = 'accounts:login'
    
    def get_context_data(self, **kwargs):
        """Get enhanced dashboard context data with financial metrics"""
        context = super().get_context_data(**kwargs)
        
        try:
            # Get date range from request
            date_range_days = self._get_date_range()
            
            # Initialize enhanced dashboard service
            dashboard_service = EnhancedDashboardService(date_range_days)
            
            # Get all enhanced dashboard data
            dashboard_data = dashboard_service.get_complete_enhanced_dashboard_data()
            
            # Add to context
            context.update(dashboard_data)
            context['user'] = self.request.user
            context['enhanced_view'] = True
            
            logger.info(f"Enhanced dashboard loaded successfully for user {self.request.user.id}")
            
        except Exception as e:
            logger.error(f"Enhanced dashboard error for user {self.request.user.id}: {e}")
            context.update(self._get_error_context())
        
        return context
    
    def _get_date_range(self) -> int:
        """Get and validate date range from request"""
        try:
            date_range = int(self.request.GET.get('range', '30'))
            # Validate range
            if date_range not in [7, 30, 90, 180, 365, 730]:
                date_range = 30
            return date_range
        except (ValueError, TypeError):
            return 30
    
    def _get_error_context(self) -> dict:
        """Get minimal context for error states"""
        return {
            'error': 'Unable to load enhanced dashboard data. Please try again.',
            'total_cases': 0,
            'cases_today': 0,
            'total_value_formatted': '$0.00',
            'today_value_formatted': '$0.00',
            'enhanced_view': True,
            'selected_range': '30',
        }


@login_required(login_url='accounts:login')
def enhanced_home_view(request):
    """
    Enhanced function-based home view with financial metrics
    """
    try:
        # Get date range from request
        selected_range = request.GET.get('range', '30')
        try:
            days_in_range = int(selected_range)
            if days_in_range not in [7, 30, 90, 180, 365, 730]:
                days_in_range = 30
                selected_range = '30'
        except ValueError:
            days_in_range = 30
            selected_range = '30'
        
        # Initialize enhanced dashboard service
        dashboard_service = EnhancedDashboardService(days_in_range)
        
        # Get all enhanced dashboard data
        context = dashboard_service.get_complete_enhanced_dashboard_data()
        
        # Add user and meta information
        context.update({
            'user': request.user,
            'selected_range': selected_range,
            'enhanced_view': True,
        })
        
        logger.info(f"Enhanced dashboard loaded successfully for user {request.user.id}")
        return render(request, 'home_enhanced.html', context)
        
    except Exception as e:
        logger.error(f"Enhanced dashboard error for user {request.user.id}: {e}")
        
        # Return error context
        error_context = {
            'error': 'Unable to load enhanced dashboard data. Please try again.',
            'debug_info': {
                'exception_type': type(e).__name__,
                'exception_msg': str(e),
                'traceback_summary': 'Check server logs for details'
            } if request.user.is_staff else None,
            'total_cases': 0,
            'cases_today': 0,
            'total_value_formatted': '$0.00',
            'today_value_formatted': '$0.00',
            'enhanced_view': True,
            'selected_range': selected_range,
            'user': request.user,
        }
        
        return render(request, 'home_enhanced.html', error_context)


@require_http_methods(["GET"])
@login_required
def enhanced_dashboard_api(request):
    """
    Enhanced API endpoint for dashboard data with financial metrics
    """
    try:
        date_range_days = int(request.GET.get('range', '30'))
        if date_range_days not in [7, 30, 90, 180, 365, 730]:
            date_range_days = 30
        
        dashboard_service = EnhancedDashboardService(date_range_days)
        data = dashboard_service.get_complete_enhanced_dashboard_data()
        
        return JsonResponse({
            'success': True,
            'data': data,
            'enhanced': True
        })
        
    except Exception as e:
        logger.error(f"Enhanced dashboard API error: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Unable to load enhanced dashboard data'
        }, status=500)


@require_http_methods(["GET"])
@login_required
def financial_metrics_api(request):
    """
    API endpoint specifically for financial metrics
    """
    try:
        date_range_days = int(request.GET.get('range', '30'))
        dashboard_service = EnhancedDashboardService(date_range_days)
        
        # Get only financial metrics for performance
        financial_data = dashboard_service.data_service.get_enhanced_basic_metrics()
        
        return JsonResponse({
            'success': True,
            'financial_metrics': {
                'total_value': float(financial_data['total_value']),
                'today_value': float(financial_data['today_value']),
                'week_value': float(financial_data['week_value']),
                'month_value': float(financial_data['month_value']),
                'range_value': float(financial_data['range_value']),
                'avg_case_value': float(financial_data['avg_case_value']),
                'month_value_growth': financial_data['month_value_growth'],
                'formatted_values': {
                    'total_value_formatted': financial_data['total_value_formatted'],
                    'today_value_formatted': financial_data['today_value_formatted'],
                    'week_value_formatted': financial_data['week_value_formatted'],
                    'month_value_formatted': financial_data['month_value_formatted'],
                    'range_value_formatted': financial_data['range_value_formatted'],
                    'avg_case_value_formatted': financial_data['avg_case_value_formatted'],
                }
            }
        })
        
    except Exception as e:
        logger.error(f"Financial metrics API error: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Unable to load financial metrics'
        }, status=500)


@require_http_methods(["POST"])
@login_required
def currency_conversion_api(request):
    """
    API endpoint for currency conversion
    """
    try:
        import json
        data = json.loads(request.body)
        
        amount = float(data.get('amount', 0))
        from_currency = data.get('from_currency', 'USD')
        to_currency = data.get('to_currency', 'USD')
        
        from ..services.enhanced_dashboard_service import FinancialDataService
        financial_service = FinancialDataService()
        
        converted_amount = financial_service.convert_currency(
            amount, from_currency, to_currency
        )
        formatted_amount = financial_service.format_currency(converted_amount, to_currency)
        
        return JsonResponse({
            'success': True,
            'converted_amount': float(converted_amount),
            'formatted_amount': formatted_amount,
            'exchange_rate': float(converted_amount) / amount if amount > 0 else 0
        })
        
    except Exception as e:
        logger.error(f"Currency conversion API error: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Unable to convert currency'
        }, status=500)


# Updated backward-compatible views
class DashboardView(LoginRequiredMixin, TemplateView):
    """
    Updated dashboard view - now with option for enhanced metrics
    """
    template_name = 'home_improved.html'
    login_url = 'accounts:login'
    
    def get_context_data(self, **kwargs):
        """Get dashboard context data using appropriate service"""
        context = super().get_context_data(**kwargs)
        
        # Check if enhanced view is requested
        enhanced = self.request.GET.get('enhanced', '').lower() == 'true'
        
        try:
            date_range_days = self._get_date_range()
            
            if enhanced:
                # Use enhanced service
                dashboard_service = EnhancedDashboardService(date_range_days)
                dashboard_data = dashboard_service.get_complete_enhanced_dashboard_data()
                context['enhanced_view'] = True
            else:
                # Use original service
                dashboard_service = DashboardService(date_range_days)
                dashboard_data = dashboard_service.get_complete_dashboard_data()
                context['enhanced_view'] = False
            
            context.update(dashboard_data)
            context['user'] = self.request.user
            
            logger.info(f"Dashboard loaded successfully for user {self.request.user.id} (enhanced: {enhanced})")
            
        except Exception as e:
            logger.error(f"Dashboard error for user {self.request.user.id}: {e}")
            context.update(self._get_error_context())
        
        return context
    
    def _get_date_range(self) -> int:
        """Get and validate date range from request"""
        try:
            date_range = int(self.request.GET.get('range', '30'))
            if date_range not in [7, 30, 90, 180, 365, 730]:
                date_range = 30
            return date_range
        except (ValueError, TypeError):
            return 30
    
    def _get_error_context(self) -> dict:
        """Get minimal context for error states"""
        return {
            'error': 'Unable to load dashboard data. Please try again.',
            'total_cases': 0,
            'cases_today': 0,
            'selected_range': '30',
        }


@login_required(login_url='accounts:login')
def home_function_view(request):
    """
    Updated function-based home view with enhanced option
    """
    # Check if enhanced view is requested
    enhanced = request.GET.get('enhanced', '').lower() == 'true'
    
    if enhanced:
        return enhanced_home_view(request)
    
    # Otherwise use original implementation
    try:
        selected_range = request.GET.get('range', '30')
        try:
            days_in_range = int(selected_range)
            if days_in_range not in [7, 30, 90, 180, 365, 730]:
                days_in_range = 30
                selected_range = '30'
        except ValueError:
            days_in_range = 30
            selected_range = '30'
        
        dashboard_service = DashboardService(days_in_range)
        context = dashboard_service.get_complete_dashboard_data()
        
        context.update({
            'user': request.user,
            'selected_range': selected_range,
            'enhanced_view': False,
        })
        
        logger.info(f"Dashboard loaded successfully for user {request.user.id}")
        return render(request, 'home_improved.html', context)
        
    except Exception as e:
        logger.error(f"Dashboard error for user {request.user.id}: {e}")
        
        error_context = {
            'error': 'Unable to load dashboard data. Please try again.',
            'total_cases': 0,
            'cases_today': 0,
            'selected_range': selected_range,
            'user': request.user,
            'enhanced_view': False,
        }
        
        return render(request, 'home_improved.html', error_context)


# Legacy aliases for backward compatibility
dashboard_api = enhanced_dashboard_api
dashboard_metrics_api = financial_metrics_api

# Legacy home function for backward compatibility  
def home(request):
    """Legacy home view - redirects to improved implementation"""
    return home_function_view(request)
