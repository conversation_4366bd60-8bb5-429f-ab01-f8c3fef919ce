"""
Executive Dashboard Views
Professional financial analytics and operational insights
"""

from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.shortcuts import render
from django.http import JsonResponse
from django.views.generic import TemplateView
from django.views.decorators.cache import cache_page
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
import logging

from ..services.executive_dashboard_service import ExecutiveDashboardService

logger = logging.getLogger(__name__)


class ExecutiveDashboardView(LoginRequiredMixin, TemplateView):
    """
    Executive dashboard with comprehensive financial and operational insights
    """
    template_name = 'executive_home.html'
    login_url = 'accounts:login'
    
    def get_context_data(self, **kwargs):
        """Get executive dashboard context"""
        context = super().get_context_data(**kwargs)
        
        try:
            # Get date range from request
            date_range_days = self._get_date_range()
            
            # Initialize service
            dashboard_service = ExecutiveDashboardService(date_range_days)
            
            # Get comprehensive data
            dashboard_data = dashboard_service.get_comprehensive_dashboard_data()
            
            # Add to context
            context.update(dashboard_data)
            context.update({
                'user': self.request.user,
                'selected_range': str(date_range_days),
                'dashboard_type': 'executive',
                'page_title': 'Executive Dashboard',
            })
            
            logger.info(f"Executive dashboard loaded for user {self.request.user.id}")
            
        except Exception as e:
            logger.error(f"Executive dashboard error: {e}")
            context.update(self._get_error_context())
        
        return context
    
    def _get_date_range(self) -> int:
        """Get and validate date range"""
        try:
            date_range = int(self.request.GET.get('range', '30'))
            if date_range not in [7, 30, 90, 180, 365, 730]:
                date_range = 30
            return date_range
        except (ValueError, TypeError):
            return 30
    
    def _get_error_context(self) -> dict:
        """Error context"""
        return {
            'error': 'Unable to load executive dashboard. Please try again.',
            'dashboard_type': 'executive',
            'total_invoiced': 0,
            'total_receivable': 0,
            'total_income': 0,
            'case_count': 0,
        }


@login_required(login_url='accounts:login')
def executive_home_view(request):
    """
    Function-based executive dashboard view
    Professional financial analytics focused on invoiced, receivable, income
    """
    try:
        # Get date range
        selected_range = request.GET.get('range', '30')
        try:
            days_in_range = int(selected_range)
            if days_in_range not in [7, 30, 90, 180, 365, 730]:
                days_in_range = 30
                selected_range = '30'
        except ValueError:
            days_in_range = 30
            selected_range = '30'
        
        # Initialize service
        dashboard_service = ExecutiveDashboardService(days_in_range)
        
        # Get comprehensive data
        context = dashboard_service.get_comprehensive_dashboard_data()
        
        # Add meta information
        context.update({
            'user': request.user,
            'selected_range': selected_range,
            'dashboard_type': 'executive',
            'page_title': 'Executive Dashboard - Financial Performance',
            'currency_symbol': '$',  # Default, can be made dynamic
        })
        
        logger.info(f"Executive dashboard loaded for user {request.user.id}")
        return render(request, 'executive_home.html', context)
        
    except Exception as e:
        logger.error(f"Executive dashboard error: {e}")
        
        # Error context
        error_context = {
            'error': 'Unable to load executive dashboard data.',
            'debug_info': {
                'exception_type': type(e).__name__,
                'exception_msg': str(e),
            } if request.user.is_staff else None,
            'user': request.user,
            'selected_range': selected_range,
            'dashboard_type': 'executive',
            'page_title': 'Executive Dashboard - Error',
            'currency_symbol': '$',
            # Fallback data
            'total_invoiced': 0,
            'total_receivable': 0,
            'total_income': 0,
            'case_count': 0,
            'total_cases': 0,
            'collection_rate': 0,
            'outstanding_rate': 0,
        }
        
        return render(request, 'executive_home.html', error_context)


@require_http_methods(["GET"])
@login_required
def executive_dashboard_api(request):
    """
    API endpoint for executive dashboard data
    """
    try:
        date_range_days = int(request.GET.get('range', '30'))
        if date_range_days not in [7, 30, 90, 180, 365, 730]:
            date_range_days = 30
        
        dashboard_service = ExecutiveDashboardService(date_range_days)
        data = dashboard_service.get_comprehensive_dashboard_data()
        
        # Convert Decimal to float for JSON serialization
        def convert_decimals(obj):
            if hasattr(obj, '__dict__'):
                for key, value in obj.__dict__.items():
                    if hasattr(value, 'quantize'):  # Decimal check
                        setattr(obj, key, float(value))
            return obj
        
        # Convert decimal values
        for key, value in data.items():
            if hasattr(value, 'quantize'):  # Decimal
                data[key] = float(value)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        for k, v in item.items():
                            if hasattr(v, 'quantize'):
                                item[k] = float(v)
        
        return JsonResponse({
            'success': True,
            'data': data,
            'dashboard_type': 'executive'
        })
        
    except Exception as e:
        logger.error(f"Executive dashboard API error: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Unable to load executive dashboard data',
            'dashboard_type': 'executive'
        }, status=500)


@require_http_methods(["GET"])
@login_required
@cache_page(60 * 10)  # Cache for 10 minutes
def financial_summary_api(request):
    """
    Lightweight API for financial summary (for widgets)
    """
    try:
        date_range_days = int(request.GET.get('range', '30'))
        dashboard_service = ExecutiveDashboardService(date_range_days)
        
        # Get only financial metrics for performance
        financial_data = dashboard_service._get_financial_performance()
        
        # Convert decimals
        for key, value in financial_data.items():
            if hasattr(value, 'quantize'):
                financial_data[key] = float(value)
        
        return JsonResponse({
            'success': True,
            'financial_summary': financial_data,
            'period': dashboard_service._get_period_description()
        })
        
    except Exception as e:
        logger.error(f"Financial summary API error: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Unable to load financial summary'
        }, status=500)


@require_http_methods(["GET"])
@login_required
def operational_metrics_api(request):
    """
    API for operational metrics
    """
    try:
        date_range_days = int(request.GET.get('range', '30'))
        dashboard_service = ExecutiveDashboardService(date_range_days)
        
        operational_data = dashboard_service._get_operational_performance()
        productivity_data = dashboard_service._get_productivity_metrics()
        
        # Convert decimals
        for key, value in productivity_data.items():
            if hasattr(value, 'quantize'):
                productivity_data[key] = float(value)
        
        return JsonResponse({
            'success': True,
            'operational': operational_data,
            'productivity': productivity_data
        })
        
    except Exception as e:
        logger.error(f"Operational metrics API error: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Unable to load operational metrics'
        }, status=500)
