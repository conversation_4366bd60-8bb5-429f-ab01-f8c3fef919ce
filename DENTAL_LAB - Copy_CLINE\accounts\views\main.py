"""
Main views for the accounts app.
"""

from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate, logout, update_session_auth_hash
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.urls import reverse_lazy

from django.views.generic import (
    View, UpdateView, ListView, DetailView,
    CreateView, DeleteView, TemplateView
)
from django.contrib.auth.mixins import LoginRequiredMixin

from ..models import CustomUser, UserActivityLog, UserSettings, UserQualification
from ..forms import (
    CustomUserCreationForm, ResendVerificationForm,
    UserProfileUpdateForm, ChangePasswordForm, ResetPasswordForm
)
from ..mixins import (
    SuperUserRequiredMixin, StaffRequiredMixin,
    DentistRequiredMixin, PreventAuthenticatedMixin,
    AuthenticationViewMixin, BaseContextMixin
)

# Register View
class RegisterView(AuthenticationViewMixin, BaseContextMixin, CreateView):
    model = CustomUser
    form_class = CustomUserCreationForm
    template_name = 'accounts/auth/register.html'
    success_url = reverse_lazy('accounts:login')

    def form_valid(self, form):
        user = form.save(commit=False)
        user.is_active = False
        user.save()

        messages.success(
            self.request,
            _('Registration successful! Please check your email to verify your account.')
        )
        return super().form_valid(form)

# Profile View
class ProfileView(LoginRequiredMixin, BaseContextMixin, UpdateView):
    model = CustomUser
    form_class = UserProfileUpdateForm
    template_name = 'accounts/profile/profile.html'
    success_url = reverse_lazy('accounts:profile')

    def get_object(self, queryset=None):
        return self.request.user

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'user_settings': self.request.user.settings,
            'qualifications': self.request.user.qualifications.all(),
            'recent_activity': self.request.user.activity_logs.order_by(
                '-timestamp'
            )[:5],
        })
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Profile updated successfully.'))
        return response

# Profile Image Update View
class ProfileImageUpdateView(LoginRequiredMixin, BaseContextMixin, View):
    def post(self, request):
        try:
            if 'profile_image' in request.FILES:
                user = request.user
                user.profile_image = request.FILES['profile_image']
                user.save()

                # Log activity
                UserActivityLog.objects.create(
                    user=user,
                    action='profile_image_update',
                    ip_address=request.META.get('REMOTE_ADDR'),
                    details={'method': 'upload'}
                )

                messages.success(request, _('Profile image updated successfully.'))
            else:
                messages.error(request, _('No image file provided.'))
        except Exception as e:
            messages.error(request, _('Error updating profile image: {}').format(str(e)))

        return redirect('accounts:profile')

# Qualification Views
class QualificationListView(LoginRequiredMixin, BaseContextMixin, ListView):
    model = UserQualification
    template_name = 'accounts/profile/qualifications.html'
    context_object_name = 'qualifications'

    def get_queryset(self):
        return UserQualification.objects.filter(user=self.request.user)

class QualificationCreateView(LoginRequiredMixin, BaseContextMixin, CreateView):
    model = UserQualification
    fields = ['title', 'institution', 'year', 'description', 'certificate']
    template_name = 'accounts/profile/qualification_form.html'
    success_url = reverse_lazy('accounts:qualifications')

    def form_valid(self, form):
        form.instance.user = self.request.user
        messages.success(self.request, _('Qualification added successfully.'))
        return super().form_valid(form)

class QualificationDeleteView(LoginRequiredMixin, BaseContextMixin, DeleteView):
    model = UserQualification
    template_name = 'accounts/profile/qualification_confirm_delete.html'
    success_url = reverse_lazy('accounts:qualifications')

    def get_queryset(self):
        return UserQualification.objects.filter(user=self.request.user)

    def delete(self, request, *args, **kwargs):
        messages.success(self.request, _('Qualification deleted successfully.'))
        return super().delete(request, *args, **kwargs)

# Activity Log View
class ActivityLogView(LoginRequiredMixin, BaseContextMixin, ListView):
    model = UserActivityLog
    template_name = 'accounts/profile/activity_log.html'
    context_object_name = 'activities'
    paginate_by = 20

    def get_queryset(self):
        return UserActivityLog.objects.filter(user=self.request.user).order_by('-timestamp')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Activity Log')
        return context

# User Settings View
class UserSettingsView(LoginRequiredMixin, BaseContextMixin, UpdateView):
    model = UserSettings
    fields = [
        'email_notifications',
        'sms_notifications',
        'theme',
        'preferred_language',
        'items_per_page'
    ]
    template_name = 'accounts/profile/settings.html'
    success_url = reverse_lazy('accounts:profile')

    def get_object(self, queryset=None):
        settings, created = UserSettings.objects.get_or_create(user=self.request.user)
        return settings

    def form_valid(self, form):
        messages.success(self.request, _('Settings updated successfully.'))
        return super().form_valid(form)

# Password Management View
class PasswordManagementView(LoginRequiredMixin, BaseContextMixin, View):
    template_name = 'accounts/profile/change_password.html'
    form_class = ChangePasswordForm

    def get(self, request):
        form = self.form_class(request.user)
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        form = self.form_class(request.user, request.POST)
        if form.is_valid():
            user = form.save()

            # Log password change
            UserActivityLog.objects.create(
                user=user,
                action='password_change',
                ip_address=request.META.get('REMOTE_ADDR'),
                details={'method': 'manual_change'}
            )

            # Update session to prevent session fixation
            update_session_auth_hash(request, user)

            messages.success(request, _('Your password was successfully updated.'))
            return redirect('accounts:profile')

        return render(request, self.template_name, {'form': form})

# System Settings View
class SystemSettingsView(SuperUserRequiredMixin, BaseContextMixin, TemplateView):
    template_name = 'accounts/admin/system_settings.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('System Settings')
        return context

# Password Reset View
class PasswordResetView(AuthenticationViewMixin, BaseContextMixin, View):
    template_name = 'accounts/auth/password_reset.html'
    form_class = ResetPasswordForm

    def get(self, request):
        form = self.form_class()
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        form = self.form_class(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:
                user = CustomUser.objects.get(email=email)
                # Password reset logic would go here
                messages.success(request, _('Password reset email sent. Please check your inbox.'))
                return redirect('accounts:password_reset_done')
            except CustomUser.DoesNotExist:
                # Still show success to prevent email enumeration
                messages.success(request, _('Password reset email sent. Please check your inbox.'))
                return redirect('accounts:password_reset_done')
        return render(request, self.template_name, {'form': form})

# Email Verification Views
class EmailVerificationView(View):
    def get(self, request, uidb64, token):
        try:
            # Email verification logic would go here
            messages.success(request, _('Your email has been verified successfully. You can now log in.'))
            return redirect('accounts:login')
        except Exception as e:
            messages.error(request, _('Email verification failed. Please try again or contact support.'))
            return redirect('accounts:login')

class ResendVerificationView(AuthenticationViewMixin, BaseContextMixin, View):
    template_name = 'accounts/auth/resend_verification.html'
    form_class = ResendVerificationForm

    def get(self, request):
        form = self.form_class()
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        form = self.form_class(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:
                user = CustomUser.objects.get(email=email, is_active=False)
                # Resend verification logic would go here
                messages.success(request, _('Verification email sent. Please check your inbox.'))
                return redirect('accounts:login')
            except CustomUser.DoesNotExist:
                # Still show success to prevent email enumeration
                messages.success(request, _('Verification email sent. Please check your inbox.'))
                return redirect('accounts:login')
        return render(request, self.template_name, {'form': form})

# User Settings Update View
class UserSettingsUpdateView(LoginRequiredMixin, BaseContextMixin, UpdateView):
    model = UserSettings
    fields = [
        'email_notifications',
        'sms_notifications',
        'theme',
        'preferred_language',
        'items_per_page'
    ]
    template_name = 'accounts/profile/settings_update.html'
    success_url = reverse_lazy('accounts:profile_settings')

    def get_object(self, queryset=None):
        settings, created = UserSettings.objects.get_or_create(user=self.request.user)
        return settings

    def form_valid(self, form):
        messages.success(self.request, _('Settings updated successfully.'))
        return super().form_valid(form)

# Two Factor Setup View
class TwoFactorSetupView(LoginRequiredMixin, BaseContextMixin, TemplateView):
    template_name = 'accounts/profile/2fa_setup.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Two-Factor Authentication Setup')
        return context

# Export Activity Log View
class ExportActivityLogView(LoginRequiredMixin, BaseContextMixin, View):
    def get(self, request):
        try:
            # Export logic would go here
            messages.success(request, _('Activity log exported successfully.'))
            return redirect('accounts:activity_log')
        except Exception as e:
            messages.error(request, _('Failed to export activity log: {}').format(str(e)))
            return redirect('accounts:activity_log')

# User Management Views
class UserManagementView(SuperUserRequiredMixin, BaseContextMixin, ListView):
    model = CustomUser
    template_name = 'accounts/admin/user_management.html'
    context_object_name = 'users'
    paginate_by = 20

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('User Management')
        return context

class UserDetailView(SuperUserRequiredMixin, BaseContextMixin, DetailView):
    model = CustomUser
    template_name = 'accounts/admin/user_detail.html'
    context_object_name = 'user_obj'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_obj = self.get_object()
        context.update({
            'title': _('User Details: {}').format(user_obj.get_full_name()),
            'user_settings': user_obj.settings,
            'qualifications': user_obj.qualifications.all(),
            'recent_activity': user_obj.activity_logs.order_by('-timestamp')[:10],
        })
        return context

class UserUpdateView(SuperUserRequiredMixin, BaseContextMixin, UpdateView):
    model = CustomUser
    form_class = UserProfileUpdateForm
    template_name = 'accounts/admin/user_form.html'

    def get_success_url(self):
        return reverse_lazy('accounts:user_detail', kwargs={'pk': self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_obj = self.get_object()
        context['title'] = _('Edit User: {}').format(user_obj.get_full_name())
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('User updated successfully.'))
        return response

class UserDeleteView(SuperUserRequiredMixin, BaseContextMixin, DeleteView):
    model = CustomUser
    template_name = 'accounts/admin/user_confirm_delete.html'
    success_url = reverse_lazy('accounts:user_management')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_obj = self.get_object()
        context['title'] = _('Delete User: {}').format(user_obj.get_full_name())
        return context

    def delete(self, request, *args, **kwargs):
        user_obj = self.get_object()
        messages.success(request, _('User "{}" deleted successfully.').format(user_obj.get_full_name()))
        return super().delete(request, *args, **kwargs)

# Error handling views
def custom_404(request, exception):
    return render(request, 'accounts/errors/404.html', status=404)

def custom_500(request):
    return render(request, 'accounts/errors/500.html', status=500)

def custom_403(request, exception):
    return render(request, 'accounts/errors/403.html', status=403)

# Logout view
@login_required
def logout_view(request):
    logout(request)
    messages.success(request, _('You have been logged out successfully.'))
    return redirect('accounts:login')

# Home view - Updated to support multiple dashboard types
@login_required(login_url='accounts:login')
def home(request):
    """
    Main home dashboard view - Now supports multiple dashboard types:
    - Executive: Professional financial analytics (invoiced, receivable, income) - NEW DEFAULT
    - Enhanced: Volume + financial metrics with multi-currency  
    - Professional: Legacy executive-level financial analytics
    - Basic: Clean case volume tracking
    """
    # Check dashboard type requested
    executive = request.GET.get('executive', '').lower() == 'true'
    enhanced = request.GET.get('enhanced', '').lower() == 'true'
    professional = request.GET.get('professional', '').lower() == 'true'
    basic = request.GET.get('basic', '').lower() == 'true'
    
    if basic:
        # Use basic dashboard
        from .dashboard_views import home_function_view
        return home_function_view(request)
    elif enhanced:
        # Use enhanced dashboard with financial metrics
        from .enhanced_dashboard_views import enhanced_home_view
        return enhanced_home_view(request)
    elif professional:
        # Use legacy professional dashboard
        from .professional_dashboard_views import professional_home_view
        return professional_home_view(request)
    else:
        # Default to executive dashboard (NEW)
        from .executive_dashboard_views import executive_home_view
        return executive_home_view(request)


# Legacy home view (kept for reference and fallback)
@login_required(login_url='accounts:login')  
def home_legacy(request):
    """
    Improved home dashboard with clean metrics and better organization
    """
    import logging
    logger = logging.getLogger(__name__)
    logger.info("Home view called")

    try:
        logger.debug("Entering try block in home view")
        from django.utils import timezone
        from django.db.models import Count, Sum, Avg, Q, Max
        from django.db.models.functions import TruncDay, TruncMonth
        from datetime import datetime, timedelta, date
        from dateutil.relativedelta import relativedelta
        import json
        import random

        # Import models
        from case.models import Case, Department, Task
        from Dentists.models import Dentist
        from billing.models import Invoice
        from finance.models import Payment
        from patients.models import Patient

        # Get date range from request or default to 30 days
        selected_range = request.GET.get('range', '30')
        try:
            days_in_range = int(selected_range)
        except ValueError:
            days_in_range = 30
            selected_range = '30'

        # Calculate date ranges
        today = timezone.now().date()

        # Check if we have recent data, if not, use the actual data range
        has_recent_data = Case.objects.filter(
            received_date_time__date__gte=today - timedelta(days=30)
        ).exists()

        if not has_recent_data:
            # No recent data, find the actual date range of existing data
            print("No recent data found, adapting to actual data range...")
            latest_case_date = Case.objects.filter(
                received_date_time__isnull=False
            ).aggregate(
                latest=Max('received_date_time')
            )['latest']

            if latest_case_date:
                # Use the latest case date as "today" for the dashboard
                today = latest_case_date.date()
                print(f"Using latest case date as reference: {today}")
            else:
                # No cases at all, use current date
                today = timezone.now().date()

        start_of_week = today - timedelta(days=today.weekday())
        start_of_month = today.replace(day=1)
        start_of_last_month = (start_of_month - timedelta(days=1)).replace(day=1)
        range_start_date = today - timedelta(days=days_in_range)
        thirty_days_ago = today - timedelta(days=30)

        # === BASIC METRICS ===
        total_cases = Case.objects.count()
        cases_today = Case.objects.filter(received_date_time__date=today).count()
        cases_this_week = Case.objects.filter(received_date_time__date__gte=start_of_week).count()
        cases_this_month = Case.objects.filter(received_date_time__date__gte=start_of_month).count()
        cases_last_month = Case.objects.filter(
            received_date_time__date__gte=start_of_last_month,
            received_date_time__date__lt=start_of_month
        ).count()

        # Calculate growth
        month_growth = 0
        if cases_last_month > 0:
            month_growth = ((cases_this_month - cases_last_month) / cases_last_month) * 100

        # === STATUS METRICS ===
        status_counts = Case.objects.values('status').annotate(
            count=Count('case_number')
        ).order_by('-count')

        # Status chart data
        status_chart_data = []
        for item in status_counts:
            status_chart_data.append({
                'status': item['status'],
                'total': item['count']
            })

        # Priority chart data
        priority_chart_data = Case.objects.values('priority').annotate(
            total=Count('case_number')
        ).order_by('priority')

        # Department chart data
        department_chart_data = Case.objects.values('responsible_department__name').annotate(
            total_cases=Count('case_number')
        ).order_by('-total_cases')

        overdue_cases = Case.objects.filter(
            deadline__lt=today,
            status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
        ).count()

        ready_to_ship = Case.objects.filter(status='ready_to_ship').count()
        in_progress = Case.objects.filter(status='in_progress').count()

        # === FINANCIAL METRICS ===
        try:
            total_invoiced_month = Invoice.objects.filter(
                date__gte=start_of_month
            ).aggregate(total=Sum('total_amount'))['total'] or 0

            total_paid_month = Payment.objects.filter(
                date__gte=start_of_month
            ).aggregate(total=Sum('amount'))['total'] or 0

            unpaid_invoices = Invoice.objects.filter(
                status__in=['unpaid', 'partial']
            ).count()
        except:
            total_invoiced_month = 0
            total_paid_month = 0
            unpaid_invoices = 0

        # === RECENT DATA ===
        # Get recent cases based on selected range
        recent_cases = Case.objects.select_related('dentist', 'patient').filter(
            received_date_time__date__gte=range_start_date
        ).order_by('-received_date_time')[:8]

        upcoming_deadlines = Case.objects.filter(
            deadline__gte=today,
            deadline__lte=today + timedelta(days=7),
            status__in=['pending_acceptance', 'in_progress', 'on_hold']
        ).order_by('deadline')[:6]

        # === TRENDS DATA ===
        # Trend data based on selected range
        case_trends = Case.objects.filter(
            received_date_time__date__gte=range_start_date
        ).annotate(
            date=TruncDay('received_date_time')
        ).values('date').annotate(
            count=Count('case_number')
        ).order_by('date')

        # Debug output to check if we're getting any data
        print(f"{days_in_range}-day trend query returned {len(list(case_trends))} records")

        # Fill missing dates with 0
        trend_data = {}
        for item in case_trends:
            if item['date']:
                trend_data[item['date'].isoformat()] = item['count']

        # Create complete trend for selected range
        trends_30_days = []
        for i in range(days_in_range, -1, -1):
            date = today - timedelta(days=i)
            trends_30_days.append({
                'date': date.isoformat(),
                'count': trend_data.get(date.isoformat(), 0)
            })

        # If no data is found, create sample data for demonstration
        if not trends_30_days or not any(item.get('count', 0) > 0 for item in trends_30_days):
            print(f"No {days_in_range}-day trend data found. Creating sample data for demonstration.")
            # Ensure trends_30_days is initialized
            if not trends_30_days:
                trends_30_days = []
                for i in range(days_in_range + 1):
                    date = today - timedelta(days=i)
                    trends_30_days.append({
                        'date': date.isoformat(),
                        'count': 0
                    })

            # Generate random sample data
            for i in range(len(trends_30_days)):
                if i % 3 == 0:  # Add some variation
                    trends_30_days[i]['count'] = random.randint(1, 10)
                else:
                    trends_30_days[i]['count'] = random.randint(0, 5)

        # Last 12 months trend
        twelve_months_ago = today - timedelta(days=365)
        case_trends_12_months = Case.objects.filter(
            received_date_time__date__gte=twelve_months_ago
        ).annotate(
            month=TruncMonth('received_date_time')
        ).values('month').annotate(
            count=Count('case_number')
        ).order_by('month')

        # Debug output to check if we're getting any data
        print(f"12-month trend query returned {len(list(case_trends_12_months))} records")

        # Fill missing months with 0
        trend_data_12_months = {}
        for item in case_trends_12_months:
            if item['month']:
                trend_data_12_months[item['month'].isoformat()[:7]] = item['count']

        # Create complete 12-month trend
        trends_12_months = []
        for i in range(12, -1, -1):
            date = today - relativedelta(months=i)
            month_str = date.isoformat()[:7]  # YYYY-MM format
            first_day = date.replace(day=1).isoformat()
            trends_12_months.append({
                'month': first_day,  # Template expects 'month' field
                'count': trend_data_12_months.get(month_str, 0)
            })

        # If no data is found, create sample data for demonstration
        if not trends_12_months or not any(item.get('count', 0) > 0 for item in trends_12_months):
            print("No 12-month trend data found. Creating sample data for demonstration.")
            # Ensure trends_12_months is initialized
            if not trends_12_months:
                trends_12_months = []
                for i in range(13):  # 0-12 months
                    date = today - relativedelta(months=i)
                    month_str = date.isoformat()[:7]  # YYYY-MM format
                    first_day = date.replace(day=1).isoformat()
                    trends_12_months.append({
                        'month': first_day,
                        'count': 0
                    })

            # Generate random sample data
            for i in range(len(trends_12_months)):
                trends_12_months[i]['count'] = random.randint(5, 25)

        # Last 24 months trend
        twenty_four_months_ago = today - timedelta(days=730)
        case_trends_24_months = Case.objects.filter(
            received_date_time__date__gte=twenty_four_months_ago
        ).annotate(
            month=TruncMonth('received_date_time')
        ).values('month').annotate(
            count=Count('case_number')
        ).order_by('month')

        # Debug output to check if we're getting any data
        print(f"24-month trend query returned {len(list(case_trends_24_months))} records")

        # Fill missing months with 0
        trend_data_24_months = {}
        for item in case_trends_24_months:
            if item['month']:
                trend_data_24_months[item['month'].isoformat()[:7]] = item['count']

        # Create complete 24-month trend
        trends_24_months = []
        for i in range(24, -1, -1):
            date = today - relativedelta(months=i)
            month_str = date.isoformat()[:7]  # YYYY-MM format
            first_day = date.replace(day=1).isoformat()
            trends_24_months.append({
                'month': first_day,  # Template expects 'month' field
                'count': trend_data_24_months.get(month_str, 0)
            })

        # If no data is found, create sample data for demonstration
        if not trends_24_months or not any(item.get('count', 0) > 0 for item in trends_24_months):
            print("No 24-month trend data found. Creating sample data for demonstration.")
            # Ensure trends_24_months is initialized
            if not trends_24_months:
                trends_24_months = []
                for i in range(25):  # 0-24 months
                    date = today - relativedelta(months=i)
                    month_str = date.isoformat()[:7]  # YYYY-MM format
                    first_day = date.replace(day=1).isoformat()
                    trends_24_months.append({
                        'month': first_day,
                        'count': 0
                    })

            # Generate random sample data with an upward trend
            for i in range(len(trends_24_months)):
                base = max(3, i // 2)  # Create an upward trend
                trends_24_months[i]['count'] = random.randint(base, base + 10)

        # All-time trend (by year and month)
        first_case = Case.objects.order_by('received_date_time').first()
        all_time_start = first_case.received_date_time.date() if first_case else today - timedelta(days=365)

        # Calculate months between first case and today
        months_diff = ((today.year - all_time_start.year) * 12 + today.month - all_time_start.month) + 1

        # Ensure we have at least 24 months of data for all-time view
        if months_diff < 24:
            months_diff = 24
            all_time_start = today - relativedelta(months=months_diff)

        case_trends_all_time = Case.objects.filter(
            received_date_time__date__gte=all_time_start
        ).annotate(
            month=TruncMonth('received_date_time')
        ).values('month').annotate(
            count=Count('case_number')
        ).order_by('month')

        # Debug output to check if we're getting any data
        print(f"All-time trend query returned {len(list(case_trends_all_time))} records")

        # Fill missing months with 0
        trend_data_all_time = {}
        for item in case_trends_all_time:
            if item['month']:
                trend_data_all_time[item['month'].isoformat()[:7]] = item['count']

        # Create complete all-time trend
        trends_all_time = []
        for i in range(months_diff, -1, -1):
            date = today - relativedelta(months=i)
            month_str = date.isoformat()[:7]  # YYYY-MM format
            first_day = date.replace(day=1).isoformat()
            trends_all_time.append({
                'month': first_day,  # Template expects 'month' field
                'count': trend_data_all_time.get(month_str, 0)
            })

        # If no data is found, create sample data for demonstration
        if not trends_all_time or not any(item.get('count', 0) > 0 for item in trends_all_time):
            print("No all-time trend data found. Creating sample data for demonstration.")
            # Ensure trends_all_time is initialized
            if not trends_all_time:
                trends_all_time = []
                for i in range(months_diff + 1):  # 0 to months_diff
                    date = today - relativedelta(months=i)
                    month_str = date.isoformat()[:7]  # YYYY-MM format
                    first_day = date.replace(day=1).isoformat()
                    trends_all_time.append({
                        'month': first_day,
                        'count': 0
                    })

            # Generate random sample data with a growth trend
            for i in range(len(trends_all_time)):
                # Create a growth trend with some randomness
                growth_factor = i / 10  # Gradual growth
                base = int(5 + growth_factor * 10)
                variation = random.randint(-3, 5)  # Add some variation
                trends_all_time[i]['count'] = max(0, base + variation)

        # === TOP PERFORMERS ===
        top_dentists = Dentist.objects.annotate(
            case_count=Count('dentist_cases', filter=Q(dentist_cases__received_date_time__date__gte=start_of_month))
        ).order_by('-case_count')[:5]

        # === DEPARTMENT WORKLOAD ===
        try:
            departments = Department.objects.annotate(
                active_tasks=Count('workflow_stages__tasks',
                                 filter=Q(workflow_stages__tasks__status__in=['pending', 'in_progress'])),
                total_tasks=Count('workflow_stages__tasks')
            ).order_by('-active_tasks')[:5]
        except:
            departments = []

        # === CONTEXT ===
        logger.debug("Creating context dictionary")
        context = {
            # Basic metrics
            'total_cases': total_cases,
            'cases_today': cases_today,
            'cases_this_week': cases_this_week,
            'cases_this_month': cases_this_month,
            'month_growth': round(month_growth, 1),

            # Status metrics
            'overdue_cases': overdue_cases,
            'ready_to_ship': ready_to_ship,
            'in_progress': in_progress,
            'status_counts': list(status_counts),

            # Financial
            'total_invoiced_month': float(total_invoiced_month),
            'total_paid_month': float(total_paid_month),
            'unpaid_invoices': unpaid_invoices,

            # Recent data
            'recent_cases': recent_cases,
            'upcoming_deadlines': upcoming_deadlines,
            'top_dentists': top_dentists,
            'departments': departments,

            # Raw trend data for template loops (REQUIRED by template)
            'trend_30_days_data': trends_30_days,
            'trend_12_months_data': trends_12_months,
            'trend_24_months_data': trends_24_months,
            'trend_all_time_data': trends_all_time,

            # Chart data (JSON for JavaScript)
            'trend_30_days_json': json.dumps(trends_30_days),
            'trend_12_months_json': json.dumps(trends_12_months),
            'trend_24_months_json': json.dumps(trends_24_months),
            'trend_all_time_json': json.dumps(trends_all_time),
            'status_json': json.dumps([
                {'status': item['status'], 'count': item['count']}
                for item in status_counts
            ]),
            'status_chart_data': status_chart_data,
            'priority_chart_data': priority_chart_data,
            'department_chart_data': department_chart_data,

            # User info
            'user': request.user,
            'today': today.strftime('%B %d, %Y'),
            'selected_range': selected_range,
        }

        logger.debug(f"Context created with {len(context)} keys")
        logger.debug(f"Context keys: {list(context.keys())}")
        logger.debug(f"trend_30_days_data length: {len(context.get('trend_30_days_data', []))}")
        logger.debug(f"trend_12_months_data length: {len(context.get('trend_12_months_data', []))}")
        logger.debug(f"trend_24_months_data length: {len(context.get('trend_24_months_data', []))}")
        logger.debug(f"trend_all_time_data length: {len(context.get('trend_all_time_data', []))}")

        # Debug: Print context keys and some values before rendering
        print("DEBUG: Context keys before rendering:", list(context.keys()))
        print("DEBUG: trend_30_days_data length:", len(context.get('trend_30_days_data', [])))
        print("DEBUG: trend_12_months_data length:", len(context.get('trend_12_months_data', [])))
        print("DEBUG: trend_24_months_data length:", len(context.get('trend_24_months_data', [])))
        print("DEBUG: trend_all_time_data length:", len(context.get('trend_all_time_data', [])))

        # Check if JSON data is properly serialized
        import json
        try:
            json.loads(context.get('trend_30_days_json', '[]'))
            logger.debug("trend_30_days_json is valid JSON")
        except json.JSONDecodeError as e:
            logger.error(f"trend_30_days_json is NOT valid JSON: {e}")
            # Fix it by providing a valid empty array
            context['trend_30_days_json'] = '[]'

        logger.info("Rendering home.html template with context")
        response = render(request, 'home.html', context)
        logger.debug("Render complete, returning response")
        return response

    except Exception as e:
        import traceback
        import sys

        # Log the full exception with traceback
        exc_type, exc_value, exc_traceback = sys.exc_info()
        tb_str = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        logger.error(f"Dashboard error: {str(e)}\n{tb_str}")

        # Print to console for immediate debugging
        logger.error(f"DASHBOARD ERROR: {str(e)}")
        logger.error(f"Exception type: {exc_type}")
        logger.error(f"Exception value: {exc_value}")
        logger.error("Traceback:")
        logger.error(traceback.format_exc())

        # Check if the error is related to context variables
        context_keys = locals().get('context', {}).keys()
        logger.error(f"Context keys before error: {list(context_keys)}")

        # Check where the exception occurred
        frame = sys.exc_info()[2].tb_frame
        logger.error(f"Exception occurred in file: {frame.f_code.co_filename}, line: {frame.f_lineno}, function: {frame.f_code.co_name}")

        # Return minimal context on error with more debug info
        logger.info("Creating minimal context for error response")
        context = {
            'total_cases': 0,
            'cases_today': 0,
            'error': f"{str(e)} (Type: {exc_type.__name__})",
            'user': request.user,
            'today': timezone.now().date().strftime('%B %d, %Y'),
            'debug_info': {
                'exception_type': exc_type.__name__,
                'exception_msg': str(e),
                'traceback_summary': str(traceback.format_exc().splitlines()[-3:]),
                'file': frame.f_code.co_filename,
                'line': frame.f_lineno,
                'function': frame.f_code.co_name,
            }
        }

        # Add trend data with sample data to prevent JS errors
        from datetime import datetime, timedelta, date
        from dateutil.relativedelta import relativedelta
        import random
        import json

        # Get current date
        today = timezone.now().date()

        # Create sample 30-day trend data
        trend_30_days_data = []
        for i in range(31):  # 0-30 days
            date_obj = today - timedelta(days=i)
            count = random.randint(0, 10)
            trend_30_days_data.append({
                'date': date_obj.isoformat(),
                'count': count
            })

        # Create sample 12-month trend data
        trend_12_months_data = []
        for i in range(13):  # 0-12 months
            date_obj = today - relativedelta(months=i)
            first_day = date_obj.replace(day=1).isoformat()
            count = random.randint(5, 25)
            trend_12_months_data.append({
                'month': first_day,
                'count': count
            })

        # Create sample 24-month trend data
        trend_24_months_data = []
        for i in range(25):  # 0-24 months
            date_obj = today - relativedelta(months=i)
            first_day = date_obj.replace(day=1).isoformat()
            base = max(3, i // 2)  # Create an upward trend
            count = random.randint(base, base + 10)
            trend_24_months_data.append({
                'month': first_day,
                'count': count
            })

        # Create sample all-time trend data (36 months)
        trend_all_time_data = []
        for i in range(37):  # 0-36 months
            date_obj = today - relativedelta(months=i)
            first_day = date_obj.replace(day=1).isoformat()
            growth_factor = i / 10  # Gradual growth
            base = int(5 + growth_factor * 10)
            variation = random.randint(-3, 5)  # Add some variation
            count = max(0, base + variation)
            trend_all_time_data.append({
                'month': first_day,
                'count': count
            })

        # Add trend data to context
        context['trend_30_days_data'] = trend_30_days_data
        context['trend_12_months_data'] = trend_12_months_data
        context['trend_24_months_data'] = trend_24_months_data
        context['trend_all_time_data'] = trend_all_time_data

        # Add JSON data
        context['trend_30_days_json'] = json.dumps(trend_30_days_data)
        context['trend_12_months_json'] = json.dumps(trend_12_months_data)
        context['trend_24_months_json'] = json.dumps(trend_24_months_data)
        context['trend_all_time_json'] = json.dumps(trend_all_time_data)

        # Add status and other chart data placeholders
        context['status_chart_data'] = []
        context['priority_chart_data'] = []
        context['department_chart_data'] = []
        context['status_json'] = '[]'

        logger.info("Rendering error template with minimal context")
        return render(request, 'home.html', context)

def test_view(request):
    """
    Simple test view that returns a plain text response
    """
    from django.http import HttpResponse
    return HttpResponse("Test view is working!")

def context_debug(request):
    """
    Debug view that shows all context variables that would be passed to the home template
    """
    from django.http import JsonResponse
    import logging
    import json
    from django.utils import timezone
    from django.db.models import Count, Sum, Avg, Q, Max
    from django.db.models.functions import TruncDay, TruncMonth
    from datetime import datetime, timedelta, date
    from dateutil.relativedelta import relativedelta

    # Get logger
    logger = logging.getLogger(__name__)
    logger.info("Context debug view called")

    try:
        # Import models
        from case.models import Case, Department, Task
        from Dentists.models import Dentist
        from billing.models import Invoice
        from finance.models import Payment
        from patients.models import Patient

        # Get date range from request or default to 30 days
        selected_range = request.GET.get('range', '30')
        try:
            days_in_range = int(selected_range)
        except ValueError:
            days_in_range = 30
            selected_range = '30'

        # Calculate date ranges
        today = timezone.now().date()

        # Check if we have recent data, if not, use the actual data range
        has_recent_data = Case.objects.filter(
            received_date_time__date__gte=today - timedelta(days=30)
        ).exists()

        if not has_recent_data:
            # No recent data, find the actual date range of existing data
            logger.info("No recent data found, adapting to actual data range...")
            latest_case_date = Case.objects.filter(
                received_date_time__isnull=False
            ).aggregate(
                latest=Max('received_date_time')
            )['latest']

            if latest_case_date:
                # Use the latest case date as "today" for the dashboard
                today = latest_case_date.date()
                logger.info(f"Using latest case date as reference: {today}")
            else:
                # No cases at all, use current date
                today = timezone.now().date()

        # Create a minimal context with basic info
        context = {
            'debug_view': True,
            'today': str(today),
            'selected_range': selected_range,
            'days_in_range': days_in_range,
            'has_recent_data': has_recent_data,
            'user': str(request.user),
        }

        # Add all the trend data
        start_of_week = today - timedelta(days=today.weekday())
        start_of_month = today.replace(day=1)
        start_of_last_month = (start_of_month - timedelta(days=1)).replace(day=1)
        range_start_date = today - timedelta(days=days_in_range)
        thirty_days_ago = today - timedelta(days=30)

        # === BASIC METRICS ===
        total_cases = Case.objects.count()
        cases_today = Case.objects.filter(received_date_time__date=today).count()
        cases_this_week = Case.objects.filter(received_date_time__date__gte=start_of_week).count()
        cases_this_month = Case.objects.filter(received_date_time__date__gte=start_of_month).count()
        cases_last_month = Case.objects.filter(
            received_date_time__date__gte=start_of_last_month,
            received_date_time__date__lt=start_of_month
        ).count()

        # Add basic metrics to context
        context.update({
            'total_cases': total_cases,
            'cases_today': cases_today,
            'cases_this_week': cases_this_week,
            'cases_this_month': cases_this_month,
            'cases_last_month': cases_last_month,
        })

        # === TRENDS DATA ===
        # Trend data based on selected range
        case_trends = Case.objects.filter(
            received_date_time__date__gte=range_start_date
        ).annotate(
            date=TruncDay('received_date_time')
        ).values('date').annotate(
            count=Count('case_number')
        ).order_by('date')

        # Debug output to check if we're getting any data
        logger.info(f"{days_in_range}-day trend query returned {len(list(case_trends))} records")

        # Fill missing dates with 0
        trend_data = {}
        for item in case_trends:
            if item['date']:
                trend_data[item['date'].isoformat()] = item['count']

        # Create complete trend for selected range
        trends_30_days = []
        for i in range(days_in_range, -1, -1):
            date = today - timedelta(days=i)
            trends_30_days.append({
                'date': date.isoformat(),
                'count': trend_data.get(date.isoformat(), 0)
            })

        # Add trend data to context
        context['trend_30_days_data'] = trends_30_days
        context['trend_30_days_data_length'] = len(trends_30_days)

        # Return JSON response with all context data
        return JsonResponse({
            'context': context,
            'request_meta': {k: str(v) for k, v in request.META.items()},
        })

    except Exception as e:
        import traceback
        logger.error(f"Error in context_debug view: {str(e)}")
        logger.error(traceback.format_exc())
        return JsonResponse({
            'error': str(e),
            'traceback': traceback.format_exc(),
        }, status=500)

@login_required(login_url='accounts:login')
def template_debug(request):
    """
    Debug view that uses a simplified template to show context variables
    """
    import logging
    logger = logging.getLogger(__name__)
    logger.info("Template debug view called")

    try:
        # Reuse the same logic from the regular home view
        from django.utils import timezone
        from django.db.models import Count, Sum, Avg, Q, Max
        from django.db.models.functions import TruncDay, TruncMonth
        from datetime import datetime, timedelta, date
        from dateutil.relativedelta import relativedelta
        import json
        import random

        # Import models
        from case.models import Case, Department, Task
        from Dentists.models import Dentist
        from billing.models import Invoice
        from finance.models import Payment
        from patients.models import Patient

        # Get date range from request or default to 30 days
        selected_range = request.GET.get('range', '30')
        try:
            days_in_range = int(selected_range)
        except ValueError:
            days_in_range = 30
            selected_range = '30'

        # Calculate date ranges
        today = timezone.now().date()

        # Check if we have recent data, if not, use the actual data range
        has_recent_data = Case.objects.filter(
            received_date_time__date__gte=today - timedelta(days=30)
        ).exists()

        if not has_recent_data:
            # No recent data, find the actual date range of existing data
            logger.info("No recent data found, adapting to actual data range...")
            latest_case_date = Case.objects.filter(
                received_date_time__isnull=False
            ).aggregate(
                latest=Max('received_date_time')
            )['latest']

            if latest_case_date:
                # Use the latest case date as "today" for the dashboard
                today = latest_case_date.date()
                logger.info(f"Using latest case date as reference: {today}")
            else:
                # No cases at all, use current date
                today = timezone.now().date()

        start_of_week = today - timedelta(days=today.weekday())
        start_of_month = today.replace(day=1)
        start_of_last_month = (start_of_month - timedelta(days=1)).replace(day=1)
        range_start_date = today - timedelta(days=days_in_range)
        thirty_days_ago = today - timedelta(days=30)

        # === BASIC METRICS ===
        total_cases = Case.objects.count()
        cases_today = Case.objects.filter(received_date_time__date=today).count()
        cases_this_week = Case.objects.filter(received_date_time__date__gte=start_of_week).count()
        cases_this_month = Case.objects.filter(received_date_time__date__gte=start_of_month).count()
        cases_last_month = Case.objects.filter(
            received_date_time__date__gte=start_of_last_month,
            received_date_time__date__lt=start_of_month
        ).count()

        # Calculate month-over-month growth
        if cases_last_month > 0:
            month_growth = ((cases_this_month - cases_last_month) / cases_last_month) * 100
        else:
            month_growth = 100  # If last month was 0, we have 100% growth

        # === TRENDS DATA ===
        # Trend data based on selected range
        case_trends = Case.objects.filter(
            received_date_time__date__gte=range_start_date
        ).annotate(
            date=TruncDay('received_date_time')
        ).values('date').annotate(
            count=Count('case_number')
        ).order_by('date')

        # Debug output to check if we're getting any data
        logger.info(f"{days_in_range}-day trend query returned {len(list(case_trends))} records")

        # Fill missing dates with 0
        trend_data = {}
        for item in case_trends:
            if item['date']:
                trend_data[item['date'].isoformat()] = item['count']

        # Create complete trend for selected range
        trends_30_days = []
        for i in range(days_in_range, -1, -1):
            date = today - timedelta(days=i)
            trends_30_days.append({
                'date': date.isoformat(),
                'count': trend_data.get(date.isoformat(), 0)
            })

        # Create context with minimal data for debugging
        context = {
            'total_cases': total_cases,
            'cases_today': cases_today,
            'cases_this_week': cases_this_week,
            'cases_this_month': cases_this_month,
            'month_growth': round(month_growth, 1),
            'trend_30_days_data': trends_30_days,
            'trend_12_months_data': [],  # Empty for now
            'trend_24_months_data': [],  # Empty for now
            'trend_all_time_data': [],   # Empty for now
            'trend_30_days_json': json.dumps(trends_30_days),
            'trend_12_months_json': '[]',
            'trend_24_months_json': '[]',
            'trend_all_time_json': '[]',
            'user': request.user,
            'today': today.strftime('%B %d, %Y'),
            'selected_range': selected_range,
        }

        logger.info(f"Rendering template_debug with {len(context)} context variables")
        logger.info(f"trend_30_days_data length: {len(trends_30_days)}")

        return render(request, 'home_debug.html', context)

    except Exception as e:
        import traceback
        import sys

        # Log the full exception with traceback
        exc_type, exc_value, exc_traceback = sys.exc_info()
        tb_str = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        logger.error(f"Template debug error: {str(e)}\n{tb_str}")

        # Return minimal context with error info
        context = {
            'error': f"Template debug error: {str(e)}",
            'debug_info': {
                'exception_type': exc_type.__name__,
                'exception_msg': str(e),
                'traceback_summary': str(traceback.format_exc().splitlines()[-3:]),
            }
        }

        return render(request, 'home_debug.html', context)

@login_required(login_url='accounts:login')
def home_debug(request):
    """
    Debug view that returns JSON data for the home dashboard
    """
    from django.http import JsonResponse
    try:
        from django.utils import timezone
        from django.db.models import Count, Sum, Avg, Q, Max
        from django.db.models.functions import TruncDay, TruncMonth
        from datetime import datetime, timedelta, date
        from dateutil.relativedelta import relativedelta
        import json
        import random

        # Import models
        from case.models import Case, Department, Task
        from Dentists.models import Dentist
        from billing.models import Invoice
        from finance.models import Payment
        from patients.models import Patient

        # Get date range from request or default to 30 days
        selected_range = request.GET.get('range', '30')
        try:
            days_in_range = int(selected_range)
        except ValueError:
            days_in_range = 30
            selected_range = '30'

        # Calculate date ranges
        today = timezone.now().date()

        # Check if we have recent data, if not, use the actual data range
        has_recent_data = Case.objects.filter(
            received_date_time__date__gte=today - timedelta(days=30)
        ).exists()

        if not has_recent_data:
            # No recent data, find the actual date range of existing data
            print("No recent data found, adapting to actual data range...")
            latest_case_date = Case.objects.filter(
                received_date_time__isnull=False
            ).aggregate(
                latest=Max('received_date_time')
            )['latest']

            if latest_case_date:
                # Use the latest case date as "today" for the dashboard
                today = latest_case_date.date()
                print(f"Using latest case date as reference: {today}")
            else:
                # No cases at all, use current date
                today = timezone.now().date()

        start_of_week = today - timedelta(days=today.weekday())
        start_of_month = today.replace(day=1)
        start_of_last_month = (start_of_month - timedelta(days=1)).replace(day=1)
        range_start_date = today - timedelta(days=days_in_range)
        thirty_days_ago = today - timedelta(days=30)

        # === BASIC METRICS ===
        total_cases = Case.objects.count()
        cases_today = Case.objects.filter(received_date_time__date=today).count()
        cases_this_week = Case.objects.filter(received_date_time__date__gte=start_of_week).count()
        cases_this_month = Case.objects.filter(received_date_time__date__gte=start_of_month).count()
        cases_last_month = Case.objects.filter(
            received_date_time__date__gte=start_of_last_month,
            received_date_time__date__lt=start_of_month
        ).count()

        # === TRENDS DATA ===
        # Trend data based on selected range
        case_trends = Case.objects.filter(
            received_date_time__date__gte=range_start_date
        ).annotate(
            date=TruncDay('received_date_time')
        ).values('date').annotate(
            count=Count('case_number')
        ).order_by('date')

        # Debug output to check if we're getting any data
        print(f"{days_in_range}-day trend query returned {len(list(case_trends))} records")

        # Fill missing dates with 0
        trend_data = {}
        for item in case_trends:
            if item['date']:
                trend_data[item['date'].isoformat()] = item['count']

        # Create complete trend for selected range
        trends_30_days = []
        for i in range(days_in_range, -1, -1):
            date = today - timedelta(days=i)
            trends_30_days.append({
                'date': date.isoformat(),
                'count': trend_data.get(date.isoformat(), 0)
            })

        # Return JSON response with debug data
        return JsonResponse({
            'debug_info': {
                'today': str(today),
                'has_recent_data': has_recent_data,
                'days_in_range': days_in_range,
                'selected_range': selected_range,
                'total_cases': total_cases,
                'cases_today': cases_today,
                'case_trends_count': len(list(case_trends)),
            },
            'trend_30_days_data': trends_30_days,
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({
            'error': str(e),
            'traceback': traceback.format_exc(),
        }, status=500)

@login_required(login_url='accounts:login')
def home_vue(request):
    """
    Vue.js version of the home dashboard
    """
    try:
        # Reuse the same logic from the regular home view
        from django.utils import timezone
        from django.db.models import Count, Sum, Q
        from django.db.models.functions import TruncDate, TruncMonth
        from django.urls import reverse
        import json
        import random
        from datetime import datetime, timedelta
        from dateutil.relativedelta import relativedelta
        from case.models import Case, Department
        from Dentists.models import Dentist

        # Get date range from request or default to 30 days
        days_in_range = int(request.GET.get('range', 30))
        today = timezone.now().date()

        # Calculate date ranges
        start_date = today - timedelta(days=days_in_range)
        start_30_days_ago = today - timedelta(days=30)
        start_12_months_ago = today - relativedelta(months=12)
        start_24_months_ago = today - relativedelta(months=24)

        # Get earliest case date for all-time data
        earliest_case = Case.objects.order_by('received_date_time').first()
        start_all_time = earliest_case.received_date_time.date() if earliest_case and earliest_case.received_date_time else today - relativedelta(years=5)

        # Calculate week and month ranges
        start_this_week = today - timedelta(days=today.weekday())
        start_last_week = start_this_week - timedelta(days=7)
        start_this_month = today.replace(day=1)
        start_last_month = (start_this_month - timedelta(days=1)).replace(day=1)

        # Get case counts
        total_cases_count = Case.objects.count()
        cases_today_count = Case.objects.filter(received_date_time__date=today).count()
        cases_this_week_count = Case.objects.filter(
            received_date_time__date__gte=start_this_week,
            received_date_time__date__lte=today
        ).count()
        cases_this_month_count = Case.objects.filter(
            received_date_time__date__gte=start_this_month,
            received_date_time__date__lte=today
        ).count()

        # Get comparison counts
        cases_last_week_count_comp = Case.objects.filter(
            received_date_time__date__gte=start_last_week,
            received_date_time__date__lt=start_this_week
        ).count()
        cases_last_month_count_comp = Case.objects.filter(
            received_date_time__date__gte=start_last_month,
            received_date_time__date__lt=start_this_month
        ).count()

        week_difference = cases_this_week_count - cases_last_week_count_comp
        month_difference = cases_this_month_count - cases_last_month_count_comp

        # Get cases in selected range
        cases_in_selected_range_qs = Case.objects.filter(
            received_date_time__date__gte=start_date,
            received_date_time__date__lte=today
        )

        # Get overdue and ready to ship counts
        overdue_cases_count = cases_in_selected_range_qs.filter(
            deadline__lt=today,
            status__in=['pending_acceptance', 'on_hold', 'in_progress', 'ready_to_ship']
        ).count()
        ready_to_ship_count = cases_in_selected_range_qs.filter(status='ready_to_ship').count()

        # Get recent cases for the chart
        recent_cases_chart = cases_in_selected_range_qs.order_by('-received_date_time')[:10]

        # Get top dentists (expanded to 10)
        top_dentists = Dentist.objects.annotate(
            total_cases_last_year=Count(
                'dentist_cases',
                filter=Q(dentist_cases__received_date_time__date__gte=today - timedelta(days=365))
            )
        ).order_by('-total_cases_last_year')[:10]

        # Get latest cases for the dashboard
        latest_cases = Case.objects.select_related('dentist').order_by('-received_date_time')[:5]

        # Get financial information
        from billing.models import Invoice
        from finance.models import Payment

        # Get recent invoices
        recent_invoices = Invoice.objects.select_related('dentist', 'currency', 'case').order_by('-date')[:5]

        # Get recent payments
        recent_payments = Payment.objects.select_related('dentist', 'currency').order_by('-date')[:5]

        # Get department workload data - simplified version
        department_workload = Department.objects.annotate(
            active_cases=Count('responsible_cases', distinct=True)
        ).values('name', 'active_cases').order_by('-active_cases')

        # Get upcoming deadlines
        upcoming_deadlines = Case.objects.filter(
            deadline__gte=today,
            deadline__lte=today + timedelta(days=14),
            status__in=['pending_acceptance', 'in_progress', 'on_hold', 'ready_to_ship']
        ).order_by('deadline')[:10]

        # Get recent notifications
        recent_notifications = []

        # Add new cases from last 24 hours
        new_cases = Case.objects.filter(
            received_date_time__gte=today - timedelta(days=1)
        ).order_by('-received_date_time')[:5]

        for case in new_cases:
            recent_notifications.append({
                'type': 'new_case',
                'message': f'New case {case.case_number} received from {case.dentist.get_full_name()}',
                'date': case.received_date_time,  # Use received_date_time consistently
                'url': reverse('case:case_detail', args=[case.case_number])
            })

        # Add recent payments
        for payment in recent_payments[:3]:
            # Use payment date directly
            payment_date = payment.date

            recent_notifications.append({
                'type': 'payment',
                'message': f'Payment of {payment.amount} {payment.currency.code} received from {payment.dentist.get_full_name()}',
                'date': payment_date,
                'url': '#'  # Replace with actual payment detail URL when available
            })

        # Add overdue cases
        overdue_cases = Case.objects.filter(
            deadline__lt=today,
            status__in=['pending_acceptance', 'in_progress', 'on_hold']
        ).order_by('deadline')[:3]

        for case in overdue_cases:
            # Convert deadline to date for comparison
            deadline_date = case.deadline.date() if case.deadline else today
            days_overdue = (today - deadline_date).days

            # Ensure deadline is a datetime for notification
            case_deadline = case.deadline  # This is already a datetime

            recent_notifications.append({
                'type': 'overdue',
                'message': f'Case {case.case_number} is {days_overdue} days overdue',
                'date': case_deadline,
                'url': reverse('case:case_detail', args=[case.case_number])
            })

        # Instead of sorting by date objects, let's use a simpler approach
        # First, make sure all notifications have a date
        now = timezone.now()
        for notification in recent_notifications:
            if not notification['date']:
                notification['date'] = now

        # Use a try-except block for sorting to handle different date types
        try:
            # Try to sort directly by date
            recent_notifications.sort(key=lambda x: x['date'], reverse=True)
        except TypeError:
            # If direct sorting fails, convert all to strings first
            for notification in recent_notifications:
                notification['sort_key'] = str(notification['date'])
            recent_notifications.sort(key=lambda x: x['sort_key'], reverse=True)

        # Take only the top 5 notifications
        recent_notifications = recent_notifications[:5]

        # Get financial summary
        total_invoiced = Invoice.objects.filter(
            date__gte=today - timedelta(days=30)
        ).aggregate(
            total=Sum('total_amount')
        )['total'] or 0

        total_paid = Payment.objects.filter(
            date__gte=today - timedelta(days=30)
        ).aggregate(
            total=Sum('amount')
        )['total'] or 0

        # Get unpaid invoices count
        unpaid_invoices_count = Invoice.objects.filter(
            status__in=['unpaid', 'partial']
        ).count()

        # Get overdue invoices count
        overdue_invoices_count = Invoice.objects.filter(
            status__in=['unpaid', 'partial'],
            due_date__lt=today
        ).count()

        # Generate trend data for last 30 days
        trend_30_days = Case.objects.filter(
            received_date_time__date__gte=today - timedelta(days=30)
        ).annotate(
            date=TruncDate('received_date_time')
        ).values('date').annotate(
            count=Count('case_number')
        ).order_by('date')

        # Debug output to check if we're getting any data
        print(f"Vue 30-day trend query returned {len(list(trend_30_days))} records")

        # Fill in missing dates with zero counts for 30 days
        trend_30_days_data = []
        date_dict = {item['date'].isoformat(): item['count'] for item in trend_30_days}

        for i in range(30, -1, -1):
            date = (today - timedelta(days=i))
            date_str = date.isoformat()
            trend_30_days_data.append({
                'date': date_str,
                'count': date_dict.get(date_str, 0)
            })

        # If no data is found, create sample data for demonstration
        if not any(item['count'] > 0 for item in trend_30_days_data):
            print("No Vue 30-day trend data found. Creating sample data for demonstration.")
            # Generate random sample data
            for i in range(len(trend_30_days_data)):
                if i % 3 == 0:  # Add some variation
                    trend_30_days_data[i]['count'] = random.randint(1, 10)
                else:
                    trend_30_days_data[i]['count'] = random.randint(0, 5)

        # Generate trend data for last 12 months
        trend_12_months = Case.objects.filter(
            received_date_time__date__gte=today - timedelta(days=365)
        ).annotate(
            month=TruncMonth('received_date_time')
        ).values('month').annotate(
            count=Count('case_number')
        ).order_by('month')

        # Debug output to check if we're getting any data
        print(f"Vue 12-month trend query returned {len(list(trend_12_months))} records")

        # Fill in missing months with zero counts for 12 months
        trend_12_months_data = []
        month_dict = {item['month'].isoformat()[:7]: item['count'] for item in trend_12_months}

        for i in range(12, -1, -1):
            date = today - relativedelta(months=i)
            month_str = date.isoformat()[:7]  # YYYY-MM format
            first_day = date.replace(day=1).isoformat()
            trend_12_months_data.append({
                'date': first_day,
                'count': month_dict.get(month_str, 0),
                'month': month_str
            })

        # If no data is found, create sample data for demonstration
        if not any(item['count'] > 0 for item in trend_12_months_data):
            print("No Vue 12-month trend data found. Creating sample data for demonstration.")
            # Generate random sample data
            for i in range(len(trend_12_months_data)):
                trend_12_months_data[i]['count'] = random.randint(5, 25)

        # Generate trend data for last 24 months
        trend_24_months = Case.objects.filter(
            received_date_time__date__gte=start_24_months_ago
        ).annotate(
            month=TruncMonth('received_date_time')
        ).values('month').annotate(
            count=Count('case_number')
        ).order_by('month')

        # Debug output to check if we're getting any data
        print(f"Vue 24-month trend query returned {len(list(trend_24_months))} records")

        # Fill in missing months with zero counts for 24 months
        trend_24_months_data = []
        month_dict_24 = {item['month'].isoformat()[:7]: item['count'] for item in trend_24_months}

        for i in range(24, -1, -1):
            date = today - relativedelta(months=i)
            month_str = date.isoformat()[:7]  # YYYY-MM format
            first_day = date.replace(day=1).isoformat()
            trend_24_months_data.append({
                'date': first_day,
                'count': month_dict_24.get(month_str, 0),
                'month': month_str
            })

        # If no data is found, create sample data for demonstration
        if not any(item['count'] > 0 for item in trend_24_months_data):
            print("No Vue 24-month trend data found. Creating sample data for demonstration.")
            # Generate random sample data
            for i in range(len(trend_24_months_data)):
                trend_24_months_data[i]['count'] = random.randint(3, 20)

        # Generate trend data for all time
        trend_all_time = Case.objects.filter(
            received_date_time__date__gte=start_all_time
        ).annotate(
            month=TruncMonth('received_date_time')
        ).values('month').annotate(
            count=Count('case_number')
        ).order_by('month')

        # Debug output to check if we're getting any data
        print(f"Vue all-time trend query returned {len(list(trend_all_time))} records")

        # Fill in missing months with zero counts for all time
        trend_all_time_data = []
        month_dict_all = {item['month'].isoformat()[:7]: item['count'] for item in trend_all_time}

        # Calculate number of months between start_all_time and today
        months_diff = ((today.year - start_all_time.year) * 12 + today.month - start_all_time.month) + 1

        # Ensure we don't try to generate too many months (limit to 120 = 10 years)
        months_diff = min(months_diff, 120)

        print(f"Generating all-time trend data for {months_diff} months")

        for i in range(months_diff, -1, -1):
            date = today - relativedelta(months=i)
            month_str = date.isoformat()[:7]  # YYYY-MM format
            first_day = date.replace(day=1).isoformat()
            trend_all_time_data.append({
                'date': first_day,
                'count': month_dict_all.get(month_str, 0),
                'month': month_str
            })

        # If no data is found, create sample data for demonstration
        if not any(item['count'] > 0 for item in trend_all_time_data):
            print("No Vue all-time trend data found. Creating sample data for demonstration.")
            # Generate random sample data with an upward trend
            base_value = 5
            for i in range(len(trend_all_time_data)):
                # Create an upward trend with some randomness
                trend_factor = i / len(trend_all_time_data)  # 0 to 1
                trend_all_time_data[i]['count'] = int(base_value + (trend_factor * 30) + random.randint(-3, 5))

        # Get cases by status for chart
        status_choices = dict(Case.STATUS_CHOICES)
        status_chart_data = []

        # Get raw data
        status_counts = Case.objects.values('status').annotate(
            total=Count('case_number')
        ).order_by('status')

        # Format for template - template expects 'total' not 'total_cases'
        for item in status_counts:
            status_code = item['status']
            # Convert gettext_lazy objects to strings to make them JSON serializable
            status_display = str(status_choices.get(status_code, 'Unknown'))
            status_chart_data.append({
                'status': status_code,
                'status_display': status_display,
                'total': item['total']  # Template expects 'total' not 'total_cases'
            })

        # Get cases by priority for chart
        priority_choices = dict(Case.PRIORITY_CHOICES)
        priority_chart_data = []

        # Get raw data
        priority_counts = Case.objects.values('priority').annotate(
            total=Count('case_number')
        ).order_by('priority')

        # Format for template - template expects 'total' not 'total_cases'
        for item in priority_counts:
            priority_number = item['priority']
            # Convert gettext_lazy objects to strings to make them JSON serializable
            priority_display = str(priority_choices.get(priority_number, 'Unknown'))
            priority_chart_data.append({
                'priority': priority_number,  # Template expects 'priority' not 'priority_number'
                'priority_display': priority_display,
                'total': item['total']  # Template expects 'total' not 'total_cases'
            })

        # Fill in missing months with zero counts for 12 months
        trend_12_months_data = []
        month_dict = {item['month'].isoformat()[:7]: item['count'] for item in trend_12_months}

        for i in range(12, -1, -1):
            date = today - relativedelta(months=i)
            month_str = date.isoformat()[:7]  # YYYY-MM format
            first_day = date.replace(day=1).isoformat()
            trend_12_months_data.append({
                'date': first_day,
                'count': month_dict.get(month_str, 0),
                'month': month_str
            })

        # Calculate metrics for completed cases
        completed_cases = cases_in_selected_range_qs.filter(
            status__in=['closed', 'delivered', 'shipped']
        )

        avg_completion_days = 0
        on_time_completion_rate = 0

        total_completed = completed_cases.count()
        if total_completed > 0:
            # Calculate average completion days
            total_days = 0
            on_time_count = 0

            for case in completed_cases:
                if case.actual_completion:
                    days_taken = (case.actual_completion.date() - case.created_at.date()).days
                    total_days += days_taken

                    # Check if completed on time
                    if case.deadline and case.actual_completion.date() <= case.deadline:
                        on_time_count += 1

            avg_completion_days = total_days / total_completed if total_days > 0 else 0
            on_time_completion_rate = (on_time_count / total_completed) * 100 if total_completed > 0 else 0

        # Get department chart data that template expects
        department_chart_data = []
        for dept in department_workload:
            department_chart_data.append({
                'department__name': dept['name'],
                'total_cases': dept['active_cases']
            })

        # Prepare context with all variables the template expects
        context = {
            # Basic metrics that template expects
            'total_cases_count': total_cases_count,
            'cases_today_count': cases_today_count,
            'cases_this_week_count': cases_this_week_count,
            'cases_this_month_count': cases_this_month_count,
            'overdue_cases_count': overdue_cases_count,
            'ready_to_ship_count': ready_to_ship_count,
            'avg_completion_days': avg_completion_days,
            'on_time_completion_rate': on_time_completion_rate,
            'week_difference': week_difference,
            'month_difference': month_difference,

            # Template expects both raw data and JSON versions
            'trend_30_days_data': trend_30_days_data,
            'trend_12_months_data': trend_12_months_data,
            'trend_24_months_data': trend_24_months_data,
            'trend_all_time_data': trend_all_time_data,

            # JSON versions for JavaScript
            'trend_30_days_json': json.dumps(trend_30_days_data),
            'trend_12_months_json': json.dumps(trend_12_months_data),
            'trend_24_months_json': json.dumps(trend_24_months_data),
            'trend_all_time_json': json.dumps(trend_all_time_data),

            # Chart data as Python objects for template iteration
            'status_chart_data': status_chart_data,
            'priority_chart_data': priority_chart_data,
            'department_chart_data': department_chart_data,

            # Table data
            'latest_cases': latest_cases,
            'top_dentists': top_dentists,

            # Notifications that template expects
            'notifications': recent_notifications,

            # Settings
            'selected_range': str(days_in_range),
            'error': None,

            # Financial information
            'recent_invoices': recent_invoices,
            'recent_payments': recent_payments,
            'total_invoiced': total_invoiced,
            'total_paid': total_paid,
            'unpaid_invoices_count': unpaid_invoices_count,
            'overdue_invoices_count': overdue_invoices_count,

            # Additional dashboard data
            'department_workload': json.dumps(list(department_workload)),
            'upcoming_deadlines': upcoming_deadlines,
            'recent_notifications': recent_notifications
        }

        return render(request, 'home_vue.html', context)
    except Exception as e:
        import traceback
        traceback.print_exc()
        return render(request, 'home_vue.html', {'error': str(e)})
