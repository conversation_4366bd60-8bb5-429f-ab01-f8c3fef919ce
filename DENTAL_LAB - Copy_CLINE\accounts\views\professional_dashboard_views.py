"""
Professional Dashboard Views
Executive-level financial analytics with sophisticated presentation
"""

from django.contrib.auth.decorators import login_required
from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.cache import cache_page
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
import logging

from ..services.professional_dashboard_service import ProfessionalDashboardService

logger = logging.getLogger(__name__)


class ProfessionalDashboardView(LoginRequiredMixin, TemplateView):
    """
    Professional executive dashboard with sophisticated financial analytics
    """
    template_name = 'professional_dashboard.html'
    login_url = 'accounts:login'
    
    def get_context_data(self, **kwargs):
        """Get professional dashboard context data"""
        context = super().get_context_data(**kwargs)
        
        try:
            # Get date range from request
            date_range_days = self._get_date_range()
            
            # Initialize professional dashboard service
            dashboard_service = ProfessionalDashboardService(date_range_days)
            
            # Get all professional dashboard data
            dashboard_data = dashboard_service.get_complete_dashboard_data()
            
            # Add to context
            context.update(dashboard_data)
            context['user'] = self.request.user
            context['professional_view'] = True
            
            logger.info(f"Professional dashboard loaded successfully for user {self.request.user.id}")
            
        except Exception as e:
            logger.error(f"Professional dashboard error for user {self.request.user.id}: {e}")
            context.update(self._get_error_context())
        
        return context
    
    def _get_date_range(self) -> int:
        """Get and validate date range from request"""
        try:
            date_range = int(self.request.GET.get('range', '30'))
            if date_range not in [7, 30, 90, 180, 365, 730]:
                date_range = 30
            return date_range
        except (ValueError, TypeError):
            return 30
    
    def _get_error_context(self) -> dict:
        """Get minimal context for error states"""
        return {
            'error': 'Unable to load professional dashboard data. Please try again.',
            'total_cases': 0,
            'active_cases': 0,
            'mtd_invoiced_formatted': '$0.00',
            'mtd_income_formatted': '$0.00',
            'professional_view': True,
            'selected_range': '30',
        }


@login_required(login_url='accounts:login')
def professional_home_view(request):
    """
    Professional function-based dashboard view
    """
    try:
        # Get date range from request
        selected_range = request.GET.get('range', '30')
        try:
            days_in_range = int(selected_range)
            if days_in_range not in [7, 30, 90, 180, 365, 730]:
                days_in_range = 30
                selected_range = '30'
        except ValueError:
            days_in_range = 30
            selected_range = '30'
        
        # Initialize professional dashboard service
        dashboard_service = ProfessionalDashboardService(days_in_range)
        
        # Get all professional dashboard data
        context = dashboard_service.get_complete_dashboard_data()
        
        # Add user and meta information
        context.update({
            'user': request.user,
            'selected_range': selected_range,
            'professional_view': True,
        })
        
        logger.info(f"Professional dashboard loaded successfully for user {request.user.id}")
        return render(request, 'professional_dashboard.html', context)
        
    except Exception as e:
        logger.error(f"Professional dashboard error for user {request.user.id}: {e}")
        
        # Return error context
        error_context = {
            'error': 'Unable to load professional dashboard data. Please try again.',
            'debug_info': {
                'exception_type': type(e).__name__,
                'exception_msg': str(e),
                'traceback_summary': 'Check server logs for details'
            } if request.user.is_staff else None,
            'total_cases': 0,
            'active_cases': 0,
            'mtd_invoiced_formatted': '$0.00',
            'mtd_income_formatted': '$0.00',
            'professional_view': True,
            'selected_range': selected_range,
            'user': request.user,
        }
        
        return render(request, 'professional_dashboard.html', error_context)


@require_http_methods(["GET"])
@login_required
def professional_dashboard_api(request):
    """
    API endpoint for professional dashboard data
    """
    try:
        date_range_days = int(request.GET.get('range', '30'))
        if date_range_days not in [7, 30, 90, 180, 365, 730]:
            date_range_days = 30
        
        dashboard_service = ProfessionalDashboardService(date_range_days)
        data = dashboard_service.get_complete_dashboard_data()
        
        return JsonResponse({
            'success': True,
            'data': data,
            'dashboard_type': 'professional'
        })
        
    except Exception as e:
        logger.error(f"Professional dashboard API error: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Unable to load professional dashboard data'
        }, status=500)


@require_http_methods(["GET"])
@login_required
@cache_page(60 * 5)  # Cache for 5 minutes
def financial_summary_api(request):
    """
    API endpoint for executive financial summary
    """
    try:
        date_range_days = int(request.GET.get('range', '30'))
        dashboard_service = ProfessionalDashboardService(date_range_days)
        
        # Get only executive summary for performance
        summary_data = dashboard_service.get_executive_summary()
        
        return JsonResponse({
            'success': True,
            'executive_summary': summary_data
        })
        
    except Exception as e:
        logger.error(f"Financial summary API error: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Unable to load financial summary'
        }, status=500)


@require_http_methods(["GET"])
@login_required
def operational_metrics_api(request):
    """
    API endpoint for operational metrics
    """
    try:
        date_range_days = int(request.GET.get('range', '30'))
        dashboard_service = ProfessionalDashboardService(date_range_days)
        
        # Get operational metrics
        operational_data = dashboard_service.get_operational_metrics()
        
        return JsonResponse({
            'success': True,
            'operational_metrics': operational_data
        })
        
    except Exception as e:
        logger.error(f"Operational metrics API error: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Unable to load operational metrics'
        }, status=500)
