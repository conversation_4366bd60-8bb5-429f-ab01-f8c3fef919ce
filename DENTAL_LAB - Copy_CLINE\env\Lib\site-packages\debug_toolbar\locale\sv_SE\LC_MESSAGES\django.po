# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
#
# Translators:
# <PERSON> <<EMAIL>>, 2012-2013
# <PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: Django Debug Toolbar\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-06 07:12-0500\n"
"PO-Revision-Date: 2010-11-30 00:00+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2012-2013\n"
"Language-Team: Swedish (Sweden) (http://app.transifex.com/django-debug-toolbar/django-debug-toolbar/language/sv_SE/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sv_SE\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: apps.py:18
msgid "Debug Toolbar"
msgstr ""

#: panels/alerts.py:67
#, python-brace-format
msgid ""
"Form with id \"{form_id}\" contains file input, but does not have the "
"attribute enctype=\"multipart/form-data\"."
msgstr ""

#: panels/alerts.py:70
msgid ""
"Form contains file input, but does not have the attribute "
"enctype=\"multipart/form-data\"."
msgstr ""

#: panels/alerts.py:73
#, python-brace-format
msgid ""
"Input element references form with id \"{form_id}\", but the form does not "
"have the attribute enctype=\"multipart/form-data\"."
msgstr ""

#: panels/alerts.py:77
msgid "Alerts"
msgstr ""

#: panels/cache.py:168
msgid "Cache"
msgstr "Cache"

#: panels/cache.py:174
#, python-format
msgid "%(cache_calls)d call in %(time).2fms"
msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgstr[0] ""
msgstr[1] ""

#: panels/cache.py:183
#, python-format
msgid "Cache calls from %(count)d backend"
msgid_plural "Cache calls from %(count)d backends"
msgstr[0] ""
msgstr[1] ""

#: panels/headers.py:31
msgid "Headers"
msgstr ""

#: panels/history/panel.py:19 panels/history/panel.py:20
msgid "History"
msgstr ""

#: panels/profiling.py:140
msgid "Profiling"
msgstr "Profilering"

#: panels/redirects.py:17
msgid "Intercept redirects"
msgstr ""

#: panels/request.py:16
msgid "Request"
msgstr ""

#: panels/request.py:38
msgid "<no view>"
msgstr ""

#: panels/request.py:55
msgid "<unavailable>"
msgstr ""

#: panels/settings.py:17
msgid "Settings"
msgstr "Inställningar"

#: panels/settings.py:20
#, python-format
msgid "Settings from %s"
msgstr ""

#: panels/signals.py:57
#, python-format
msgid "%(num_receivers)d receiver of 1 signal"
msgid_plural "%(num_receivers)d receivers of 1 signal"
msgstr[0] ""
msgstr[1] ""

#: panels/signals.py:62
#, python-format
msgid "%(num_receivers)d receiver of %(num_signals)d signals"
msgid_plural "%(num_receivers)d receivers of %(num_signals)d signals"
msgstr[0] ""
msgstr[1] ""

#: panels/signals.py:67
msgid "Signals"
msgstr "Signaler"

#: panels/sql/panel.py:30 panels/sql/panel.py:41
msgid "Read uncommitted"
msgstr ""

#: panels/sql/panel.py:31 panels/sql/panel.py:43
msgid "Read committed"
msgstr ""

#: panels/sql/panel.py:32 panels/sql/panel.py:45
msgid "Repeatable read"
msgstr ""

#: panels/sql/panel.py:33 panels/sql/panel.py:47
msgid "Serializable"
msgstr "Variabel"

#: panels/sql/panel.py:39
msgid "Autocommit"
msgstr ""

#: panels/sql/panel.py:61 panels/sql/panel.py:71
msgid "Idle"
msgstr ""

#: panels/sql/panel.py:62 panels/sql/panel.py:72
msgid "Active"
msgstr "Åtgärd"

#: panels/sql/panel.py:63 panels/sql/panel.py:73
msgid "In transaction"
msgstr ""

#: panels/sql/panel.py:64 panels/sql/panel.py:74
msgid "In error"
msgstr "Felmeddelande"

#: panels/sql/panel.py:65 panels/sql/panel.py:75
msgid "Unknown"
msgstr "(okänd)"

#: panels/sql/panel.py:162
msgid "SQL"
msgstr "SQL"

#: panels/sql/panel.py:168
#, python-format
msgid "%(query_count)d query in %(sql_time).2fms"
msgid_plural "%(query_count)d queries in %(sql_time).2fms"
msgstr[0] ""
msgstr[1] ""

#: panels/sql/panel.py:180
#, python-format
msgid "SQL queries from %(count)d connection"
msgid_plural "SQL queries from %(count)d connections"
msgstr[0] ""
msgstr[1] ""

#: panels/staticfiles.py:82
#, python-format
msgid "Static files (%(num_found)s found, %(num_used)s used)"
msgstr ""

#: panels/staticfiles.py:103
msgid "Static files"
msgstr "Statiska filer"

#: panels/staticfiles.py:109
#, python-format
msgid "%(num_used)s file used"
msgid_plural "%(num_used)s files used"
msgstr[0] ""
msgstr[1] ""

#: panels/templates/panel.py:101
msgid "Templates"
msgstr "Mallar"

#: panels/templates/panel.py:106
#, python-format
msgid "Templates (%(num_templates)s rendered)"
msgstr ""

#: panels/templates/panel.py:195
msgid "No origin"
msgstr ""

#: panels/timer.py:27
#, python-format
msgid "CPU: %(cum)0.2fms (%(total)0.2fms)"
msgstr ""

#: panels/timer.py:32
#, python-format
msgid "Total: %0.2fms"
msgstr ""

#: panels/timer.py:38 templates/debug_toolbar/panels/history.html:9
#: templates/debug_toolbar/panels/sql_explain.html:11
#: templates/debug_toolbar/panels/sql_profile.html:12
#: templates/debug_toolbar/panels/sql_select.html:11
msgid "Time"
msgstr "Tid"

#: panels/timer.py:46
msgid "User CPU time"
msgstr ""

#: panels/timer.py:46
#, python-format
msgid "%(utime)0.3f msec"
msgstr ""

#: panels/timer.py:47
msgid "System CPU time"
msgstr ""

#: panels/timer.py:47
#, python-format
msgid "%(stime)0.3f msec"
msgstr ""

#: panels/timer.py:48
msgid "Total CPU time"
msgstr ""

#: panels/timer.py:48
#, python-format
msgid "%(total)0.3f msec"
msgstr ""

#: panels/timer.py:49
msgid "Elapsed time"
msgstr ""

#: panels/timer.py:49
#, python-format
msgid "%(total_time)0.3f msec"
msgstr ""

#: panels/timer.py:51
msgid "Context switches"
msgstr ""

#: panels/timer.py:52
#, python-format
msgid "%(vcsw)d voluntary, %(ivcsw)d involuntary"
msgstr ""

#: panels/versions.py:19
msgid "Versions"
msgstr "Versioner"

#: templates/debug_toolbar/base.html:23
msgid "Hide toolbar"
msgstr ""

#: templates/debug_toolbar/base.html:23
msgid "Hide"
msgstr "Dölj"

#: templates/debug_toolbar/base.html:25 templates/debug_toolbar/base.html:26
msgid "Toggle Theme"
msgstr ""

#: templates/debug_toolbar/base.html:35
msgid "Show toolbar"
msgstr ""

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Disable for next and successive requests"
msgstr ""

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Enable for next and successive requests"
msgstr ""

#: templates/debug_toolbar/panels/alerts.html:4
msgid "Alerts found"
msgstr ""

#: templates/debug_toolbar/panels/alerts.html:11
msgid "No alerts found"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:2
msgid "Summary"
msgstr "Sammanfattning"

#: templates/debug_toolbar/panels/cache.html:6
msgid "Total calls"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:7
msgid "Total time"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:8
msgid "Cache hits"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:9
msgid "Cache misses"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:21
msgid "Commands"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:39
msgid "Calls"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:43
#: templates/debug_toolbar/panels/sql.html:36
msgid "Time (ms)"
msgstr "Tid (ms)"

#: templates/debug_toolbar/panels/cache.html:44
msgid "Type"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:45
#: templates/debug_toolbar/panels/request.html:8
msgid "Arguments"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:46
#: templates/debug_toolbar/panels/request.html:9
msgid "Keyword arguments"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:47
msgid "Backend"
msgstr ""

#: templates/debug_toolbar/panels/headers.html:3
msgid "Request headers"
msgstr ""

#: templates/debug_toolbar/panels/headers.html:8
#: templates/debug_toolbar/panels/headers.html:27
#: templates/debug_toolbar/panels/headers.html:48
msgid "Key"
msgstr "Nyckel"

#: templates/debug_toolbar/panels/headers.html:9
#: templates/debug_toolbar/panels/headers.html:28
#: templates/debug_toolbar/panels/headers.html:49
#: templates/debug_toolbar/panels/history_tr.html:23
#: templates/debug_toolbar/panels/request_variables.html:12
#: templates/debug_toolbar/panels/settings.html:6
#: templates/debug_toolbar/panels/timer.html:11
msgid "Value"
msgstr "Värde"

#: templates/debug_toolbar/panels/headers.html:22
msgid "Response headers"
msgstr ""

#: templates/debug_toolbar/panels/headers.html:41
msgid "WSGI environ"
msgstr ""

#: templates/debug_toolbar/panels/headers.html:43
msgid ""
"Since the WSGI environ inherits the environment of the server, only a "
"significant subset is shown below."
msgstr ""

#: templates/debug_toolbar/panels/history.html:10
msgid "Method"
msgstr ""

#: templates/debug_toolbar/panels/history.html:11
#: templates/debug_toolbar/panels/staticfiles.html:43
msgid "Path"
msgstr "Sökväg"

#: templates/debug_toolbar/panels/history.html:12
msgid "Request Variables"
msgstr ""

#: templates/debug_toolbar/panels/history.html:13
msgid "Status"
msgstr ""

#: templates/debug_toolbar/panels/history.html:14
#: templates/debug_toolbar/panels/sql.html:37
msgid "Action"
msgstr "Åtgärd"

#: templates/debug_toolbar/panels/history_tr.html:22
#: templates/debug_toolbar/panels/request_variables.html:11
msgid "Variable"
msgstr "Variabel"

#: templates/debug_toolbar/panels/profiling.html:5
msgid "Call"
msgstr ""

#: templates/debug_toolbar/panels/profiling.html:6
msgid "CumTime"
msgstr ""

#: templates/debug_toolbar/panels/profiling.html:7
#: templates/debug_toolbar/panels/profiling.html:9
msgid "Per"
msgstr ""

#: templates/debug_toolbar/panels/profiling.html:8
msgid "TotTime"
msgstr ""

#: templates/debug_toolbar/panels/profiling.html:10
msgid "Count"
msgstr "Räkna"

#: templates/debug_toolbar/panels/request.html:3
msgid "View information"
msgstr "Visa informationen"

#: templates/debug_toolbar/panels/request.html:7
msgid "View function"
msgstr ""

#: templates/debug_toolbar/panels/request.html:10
msgid "URL name"
msgstr ""

#: templates/debug_toolbar/panels/request.html:24
msgid "Cookies"
msgstr ""

#: templates/debug_toolbar/panels/request.html:27
msgid "No cookies"
msgstr ""

#: templates/debug_toolbar/panels/request.html:31
msgid "Session data"
msgstr ""

#: templates/debug_toolbar/panels/request.html:34
msgid "No session data"
msgstr ""

#: templates/debug_toolbar/panels/request.html:38
msgid "GET data"
msgstr ""

#: templates/debug_toolbar/panels/request.html:41
msgid "No GET data"
msgstr "Ingen GET data"

#: templates/debug_toolbar/panels/request.html:45
msgid "POST data"
msgstr ""

#: templates/debug_toolbar/panels/request.html:48
msgid "No POST data"
msgstr "Ingen POST data"

#: templates/debug_toolbar/panels/settings.html:5
msgid "Setting"
msgstr "Inställning"

#: templates/debug_toolbar/panels/signals.html:5
msgid "Signal"
msgstr "Signal"

#: templates/debug_toolbar/panels/signals.html:6
msgid "Receivers"
msgstr "Mottagare"

#: templates/debug_toolbar/panels/sql.html:6
#, python-format
msgid "%(num)s query"
msgid_plural "%(num)s queries"
msgstr[0] ""
msgstr[1] ""

#: templates/debug_toolbar/panels/sql.html:8
#, python-format
msgid ""
"including <abbr title=\"Similar queries are queries with the same SQL, but "
"potentially different parameters.\">%(count)s similar</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:12
#, python-format
msgid ""
"and <abbr title=\"Duplicate queries are identical to each other: they "
"execute exactly the same SQL and parameters.\">%(dupes)s duplicates</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:34
msgid "Query"
msgstr "Fråga"

#: templates/debug_toolbar/panels/sql.html:35
#: templates/debug_toolbar/panels/timer.html:36
msgid "Timeline"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:52
#, python-format
msgid "%(count)s similar queries."
msgstr ""

#: templates/debug_toolbar/panels/sql.html:58
#, python-format
msgid "Duplicated %(dupes)s times."
msgstr ""

#: templates/debug_toolbar/panels/sql.html:95
msgid "Connection:"
msgstr "Anslutning:"

#: templates/debug_toolbar/panels/sql.html:97
msgid "Isolation level:"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:100
msgid "Transaction status:"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:114
msgid "(unknown)"
msgstr "(okänd)"

#: templates/debug_toolbar/panels/sql.html:123
msgid "No SQL queries were recorded during this request."
msgstr ""

#: templates/debug_toolbar/panels/sql_explain.html:4
msgid "SQL explained"
msgstr ""

#: templates/debug_toolbar/panels/sql_explain.html:9
#: templates/debug_toolbar/panels/sql_profile.html:10
#: templates/debug_toolbar/panels/sql_select.html:9
msgid "Executed SQL"
msgstr "Utförd SQL"

#: templates/debug_toolbar/panels/sql_explain.html:13
#: templates/debug_toolbar/panels/sql_profile.html:14
#: templates/debug_toolbar/panels/sql_select.html:13
msgid "Database"
msgstr "Databas"

#: templates/debug_toolbar/panels/sql_profile.html:4
msgid "SQL profiled"
msgstr ""

#: templates/debug_toolbar/panels/sql_profile.html:37
msgid "Error"
msgstr "Felmeddelande"

#: templates/debug_toolbar/panels/sql_select.html:4
msgid "SQL selected"
msgstr ""

#: templates/debug_toolbar/panels/sql_select.html:36
msgid "Empty set"
msgstr "Tomt set"

#: templates/debug_toolbar/panels/staticfiles.html:3
msgid "Static file path"
msgid_plural "Static file paths"
msgstr[0] ""
msgstr[1] ""

#: templates/debug_toolbar/panels/staticfiles.html:7
#, python-format
msgid "(prefix %(prefix)s)"
msgstr ""

#: templates/debug_toolbar/panels/staticfiles.html:11
#: templates/debug_toolbar/panels/staticfiles.html:22
#: templates/debug_toolbar/panels/staticfiles.html:34
#: templates/debug_toolbar/panels/templates.html:10
#: templates/debug_toolbar/panels/templates.html:30
#: templates/debug_toolbar/panels/templates.html:47
msgid "None"
msgstr "Inget"

#: templates/debug_toolbar/panels/staticfiles.html:14
msgid "Static file app"
msgid_plural "Static file apps"
msgstr[0] ""
msgstr[1] ""

#: templates/debug_toolbar/panels/staticfiles.html:25
msgid "Static file"
msgid_plural "Static files"
msgstr[0] ""
msgstr[1] "Statiska filer"

#: templates/debug_toolbar/panels/staticfiles.html:39
#, python-format
msgid "%(payload_count)s file"
msgid_plural "%(payload_count)s files"
msgstr[0] ""
msgstr[1] ""

#: templates/debug_toolbar/panels/staticfiles.html:44
msgid "Location"
msgstr "Plats"

#: templates/debug_toolbar/panels/template_source.html:4
msgid "Template source:"
msgstr ""

#: templates/debug_toolbar/panels/templates.html:2
msgid "Template path"
msgid_plural "Template paths"
msgstr[0] ""
msgstr[1] ""

#: templates/debug_toolbar/panels/templates.html:13
msgid "Template"
msgid_plural "Templates"
msgstr[0] "Mall"
msgstr[1] "Mallar"

#: templates/debug_toolbar/panels/templates.html:22
#: templates/debug_toolbar/panels/templates.html:40
msgid "Toggle context"
msgstr ""

#: templates/debug_toolbar/panels/templates.html:33
msgid "Context processor"
msgid_plural "Context processors"
msgstr[0] ""
msgstr[1] ""

#: templates/debug_toolbar/panels/timer.html:2
msgid "Resource usage"
msgstr ""

#: templates/debug_toolbar/panels/timer.html:10
msgid "Resource"
msgstr "Resurs"

#: templates/debug_toolbar/panels/timer.html:26
msgid "Browser timing"
msgstr ""

#: templates/debug_toolbar/panels/timer.html:35
msgid "Timing attribute"
msgstr ""

#: templates/debug_toolbar/panels/timer.html:37
msgid "Milliseconds since navigation start (+length)"
msgstr ""

#: templates/debug_toolbar/panels/versions.html:10
msgid "Package"
msgstr ""

#: templates/debug_toolbar/panels/versions.html:11
msgid "Name"
msgstr "Namn"

#: templates/debug_toolbar/panels/versions.html:12
msgid "Version"
msgstr "Version"

#: templates/debug_toolbar/redirect.html:10
msgid "Location:"
msgstr ""

#: templates/debug_toolbar/redirect.html:12
msgid ""
"The Django Debug Toolbar has intercepted a redirect to the above URL for "
"debug viewing purposes. You can click the above link to continue with the "
"redirect as normal."
msgstr ""

#: views.py:16
msgid ""
"Data for this panel isn't available anymore. Please reload the page and "
"retry."
msgstr ""
