# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
#
# Translators:
# <PERSON>lia Volochii <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2013
msgid ""
msgstr ""
"Project-Id-Version: Django Debug Toolbar\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-06 07:12-0500\n"
"PO-Revision-Date: 2010-11-30 00:00+0000\n"
"Last-Translator: <PERSON>lia Volochii <<EMAIL>>, 2017\n"
"Language-Team: Ukrainian (http://app.transifex.com/django-debug-toolbar/django-debug-toolbar/language/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#: apps.py:18
msgid "Debug Toolbar"
msgstr "Панель Інструментів Налагодження"

#: panels/alerts.py:67
#, python-brace-format
msgid ""
"Form with id \"{form_id}\" contains file input, but does not have the "
"attribute enctype=\"multipart/form-data\"."
msgstr ""

#: panels/alerts.py:70
msgid ""
"Form contains file input, but does not have the attribute "
"enctype=\"multipart/form-data\"."
msgstr ""

#: panels/alerts.py:73
#, python-brace-format
msgid ""
"Input element references form with id \"{form_id}\", but the form does not "
"have the attribute enctype=\"multipart/form-data\"."
msgstr ""

#: panels/alerts.py:77
msgid "Alerts"
msgstr ""

#: panels/cache.py:168
msgid "Cache"
msgstr "Кеш"

#: panels/cache.py:174
#, python-format
msgid "%(cache_calls)d call in %(time).2fms"
msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgstr[0] "%(cache_calls)d виклик за %(time).2f мс"
msgstr[1] "%(cache_calls)d виклики за %(time).2f мс"
msgstr[2] "%(cache_calls)d викликів за %(time).2f мс"
msgstr[3] "%(cache_calls)d викликів за %(time).2f мс"

#: panels/cache.py:183
#, python-format
msgid "Cache calls from %(count)d backend"
msgid_plural "Cache calls from %(count)d backends"
msgstr[0] "Виклики кешу з %(count)d бекенду"
msgstr[1] "Виклики кешу із %(count)d бекендів"
msgstr[2] "Виклики кешу із %(count)d бекендів"
msgstr[3] "Виклики кешу із %(count)d бекендів"

#: panels/headers.py:31
msgid "Headers"
msgstr "Заголовки"

#: panels/history/panel.py:19 panels/history/panel.py:20
msgid "History"
msgstr ""

#: panels/profiling.py:140
msgid "Profiling"
msgstr "Профілювання"

#: panels/redirects.py:17
msgid "Intercept redirects"
msgstr "Переривати запити"

#: panels/request.py:16
msgid "Request"
msgstr "Запит"

#: panels/request.py:38
msgid "<no view>"
msgstr "<немає відображення>"

#: panels/request.py:55
msgid "<unavailable>"
msgstr "<відсутнє>"

#: panels/settings.py:17
msgid "Settings"
msgstr "Налаштування"

#: panels/settings.py:20
#, python-format
msgid "Settings from %s"
msgstr ""

#: panels/signals.py:57
#, python-format
msgid "%(num_receivers)d receiver of 1 signal"
msgid_plural "%(num_receivers)d receivers of 1 signal"
msgstr[0] "%(num_receivers)d отримувач 1 сигналу"
msgstr[1] "%(num_receivers)d отримувача 1 сигналу"
msgstr[2] "%(num_receivers)d отримувачів 1 сигналу"
msgstr[3] "%(num_receivers)d отримувачів 1 сигналу"

#: panels/signals.py:62
#, python-format
msgid "%(num_receivers)d receiver of %(num_signals)d signals"
msgid_plural "%(num_receivers)d receivers of %(num_signals)d signals"
msgstr[0] "%(num_receivers)d отримувач %(num_signals)d сигналів"
msgstr[1] "%(num_receivers)d отримувача %(num_signals)d сигналів"
msgstr[2] "%(num_receivers)d отримувачів %(num_signals)d сигналів"
msgstr[3] "%(num_receivers)d отримувачів %(num_signals)d сигналів"

#: panels/signals.py:67
msgid "Signals"
msgstr "Сигнали"

#: panels/sql/panel.py:30 panels/sql/panel.py:41
msgid "Read uncommitted"
msgstr ""

#: panels/sql/panel.py:31 panels/sql/panel.py:43
msgid "Read committed"
msgstr ""

#: panels/sql/panel.py:32 panels/sql/panel.py:45
msgid "Repeatable read"
msgstr ""

#: panels/sql/panel.py:33 panels/sql/panel.py:47
msgid "Serializable"
msgstr ""

#: panels/sql/panel.py:39
msgid "Autocommit"
msgstr "Автофіксація"

#: panels/sql/panel.py:61 panels/sql/panel.py:71
msgid "Idle"
msgstr ""

#: panels/sql/panel.py:62 panels/sql/panel.py:72
msgid "Active"
msgstr ""

#: panels/sql/panel.py:63 panels/sql/panel.py:73
msgid "In transaction"
msgstr ""

#: panels/sql/panel.py:64 panels/sql/panel.py:74
msgid "In error"
msgstr ""

#: panels/sql/panel.py:65 panels/sql/panel.py:75
msgid "Unknown"
msgstr ""

#: panels/sql/panel.py:162
msgid "SQL"
msgstr "SQL"

#: panels/sql/panel.py:168
#, python-format
msgid "%(query_count)d query in %(sql_time).2fms"
msgid_plural "%(query_count)d queries in %(sql_time).2fms"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: panels/sql/panel.py:180
#, python-format
msgid "SQL queries from %(count)d connection"
msgid_plural "SQL queries from %(count)d connections"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: panels/staticfiles.py:82
#, python-format
msgid "Static files (%(num_found)s found, %(num_used)s used)"
msgstr "Статичні файли (знайдено %(num_found)s, використано %(num_used)s)"

#: panels/staticfiles.py:103
msgid "Static files"
msgstr "Статичні файли"

#: panels/staticfiles.py:109
#, python-format
msgid "%(num_used)s file used"
msgid_plural "%(num_used)s files used"
msgstr[0] "Використано %(num_used)s файл"
msgstr[1] "Використано %(num_used)s файли"
msgstr[2] "Використано %(num_used)s файлів"
msgstr[3] "Використано %(num_used)s файлів"

#: panels/templates/panel.py:101
msgid "Templates"
msgstr "Шаблони"

#: panels/templates/panel.py:106
#, python-format
msgid "Templates (%(num_templates)s rendered)"
msgstr "Шаблони (оброблено %(num_templates)s)"

#: panels/templates/panel.py:195
msgid "No origin"
msgstr "Немає походження"

#: panels/timer.py:27
#, python-format
msgid "CPU: %(cum)0.2fms (%(total)0.2fms)"
msgstr "CPU: %(cum)0.2f мс (%(total)0.2f мс)"

#: panels/timer.py:32
#, python-format
msgid "Total: %0.2fms"
msgstr "Загалом: %0.2f мс"

#: panels/timer.py:38 templates/debug_toolbar/panels/history.html:9
#: templates/debug_toolbar/panels/sql_explain.html:11
#: templates/debug_toolbar/panels/sql_profile.html:12
#: templates/debug_toolbar/panels/sql_select.html:11
msgid "Time"
msgstr "Час"

#: panels/timer.py:46
msgid "User CPU time"
msgstr "Користувацький час CPU"

#: panels/timer.py:46
#, python-format
msgid "%(utime)0.3f msec"
msgstr "%(utime)0.3f мс"

#: panels/timer.py:47
msgid "System CPU time"
msgstr "Системний час CPU"

#: panels/timer.py:47
#, python-format
msgid "%(stime)0.3f msec"
msgstr "%(stime)0.3f мс"

#: panels/timer.py:48
msgid "Total CPU time"
msgstr "Загальний час CPU"

#: panels/timer.py:48
#, python-format
msgid "%(total)0.3f msec"
msgstr "%(total)0.3f мс"

#: panels/timer.py:49
msgid "Elapsed time"
msgstr "Витрачений час"

#: panels/timer.py:49
#, python-format
msgid "%(total_time)0.3f msec"
msgstr "%(total_time)0.3f мс"

#: panels/timer.py:51
msgid "Context switches"
msgstr "Перемикачів контексту"

#: panels/timer.py:52
#, python-format
msgid "%(vcsw)d voluntary, %(ivcsw)d involuntary"
msgstr "навмисних - %(vcsw)d, мимовільних - %(ivcsw)d"

#: panels/versions.py:19
msgid "Versions"
msgstr "Версії"

#: templates/debug_toolbar/base.html:23
msgid "Hide toolbar"
msgstr "Сховати панель інструментів"

#: templates/debug_toolbar/base.html:23
msgid "Hide"
msgstr "Сховати"

#: templates/debug_toolbar/base.html:25 templates/debug_toolbar/base.html:26
msgid "Toggle Theme"
msgstr ""

#: templates/debug_toolbar/base.html:35
msgid "Show toolbar"
msgstr "Показати панель інструментів"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Disable for next and successive requests"
msgstr "Відключити для наступного і подальших запитів"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Enable for next and successive requests"
msgstr "Включити для наступного і подальших запитів"

#: templates/debug_toolbar/panels/alerts.html:4
msgid "Alerts found"
msgstr ""

#: templates/debug_toolbar/panels/alerts.html:11
msgid "No alerts found"
msgstr ""

#: templates/debug_toolbar/panels/cache.html:2
msgid "Summary"
msgstr "Резюме"

#: templates/debug_toolbar/panels/cache.html:6
msgid "Total calls"
msgstr "Загальна кількість викликів"

#: templates/debug_toolbar/panels/cache.html:7
msgid "Total time"
msgstr "Загальний час"

#: templates/debug_toolbar/panels/cache.html:8
msgid "Cache hits"
msgstr "Кеш-попадання"

#: templates/debug_toolbar/panels/cache.html:9
msgid "Cache misses"
msgstr "Кеш-промахи"

#: templates/debug_toolbar/panels/cache.html:21
msgid "Commands"
msgstr "Команди"

#: templates/debug_toolbar/panels/cache.html:39
msgid "Calls"
msgstr "Виклики"

#: templates/debug_toolbar/panels/cache.html:43
#: templates/debug_toolbar/panels/sql.html:36
msgid "Time (ms)"
msgstr "Час (мс)"

#: templates/debug_toolbar/panels/cache.html:44
msgid "Type"
msgstr "Тип"

#: templates/debug_toolbar/panels/cache.html:45
#: templates/debug_toolbar/panels/request.html:8
msgid "Arguments"
msgstr "Аргументи"

#: templates/debug_toolbar/panels/cache.html:46
#: templates/debug_toolbar/panels/request.html:9
msgid "Keyword arguments"
msgstr "Іменовані аргументи"

#: templates/debug_toolbar/panels/cache.html:47
msgid "Backend"
msgstr "Бекенд"

#: templates/debug_toolbar/panels/headers.html:3
msgid "Request headers"
msgstr "Заголовки запиту"

#: templates/debug_toolbar/panels/headers.html:8
#: templates/debug_toolbar/panels/headers.html:27
#: templates/debug_toolbar/panels/headers.html:48
msgid "Key"
msgstr "Ключ"

#: templates/debug_toolbar/panels/headers.html:9
#: templates/debug_toolbar/panels/headers.html:28
#: templates/debug_toolbar/panels/headers.html:49
#: templates/debug_toolbar/panels/history_tr.html:23
#: templates/debug_toolbar/panels/request_variables.html:12
#: templates/debug_toolbar/panels/settings.html:6
#: templates/debug_toolbar/panels/timer.html:11
msgid "Value"
msgstr "Значення"

#: templates/debug_toolbar/panels/headers.html:22
msgid "Response headers"
msgstr "Заголовки відповіді"

#: templates/debug_toolbar/panels/headers.html:41
msgid "WSGI environ"
msgstr "Середовище WSGI"

#: templates/debug_toolbar/panels/headers.html:43
msgid ""
"Since the WSGI environ inherits the environment of the server, only a "
"significant subset is shown below."
msgstr "Оскільки середовище WSGI успадковує середовище сервера, тут показано лише найважливішу частину."

#: templates/debug_toolbar/panels/history.html:10
msgid "Method"
msgstr ""

#: templates/debug_toolbar/panels/history.html:11
#: templates/debug_toolbar/panels/staticfiles.html:43
msgid "Path"
msgstr "Шлях"

#: templates/debug_toolbar/panels/history.html:12
msgid "Request Variables"
msgstr ""

#: templates/debug_toolbar/panels/history.html:13
msgid "Status"
msgstr ""

#: templates/debug_toolbar/panels/history.html:14
#: templates/debug_toolbar/panels/sql.html:37
msgid "Action"
msgstr "Подія"

#: templates/debug_toolbar/panels/history_tr.html:22
#: templates/debug_toolbar/panels/request_variables.html:11
msgid "Variable"
msgstr "Змінна"

#: templates/debug_toolbar/panels/profiling.html:5
msgid "Call"
msgstr "Виклик"

#: templates/debug_toolbar/panels/profiling.html:6
msgid "CumTime"
msgstr "Кумул. час"

#: templates/debug_toolbar/panels/profiling.html:7
#: templates/debug_toolbar/panels/profiling.html:9
msgid "Per"
msgstr "За виклик"

#: templates/debug_toolbar/panels/profiling.html:8
msgid "TotTime"
msgstr "Заг. час"

#: templates/debug_toolbar/panels/profiling.html:10
msgid "Count"
msgstr "Кількість"

#: templates/debug_toolbar/panels/request.html:3
msgid "View information"
msgstr "Інформація про відображення"

#: templates/debug_toolbar/panels/request.html:7
msgid "View function"
msgstr "Функція відображення"

#: templates/debug_toolbar/panels/request.html:10
msgid "URL name"
msgstr "Імʼя URL"

#: templates/debug_toolbar/panels/request.html:24
msgid "Cookies"
msgstr "Куки"

#: templates/debug_toolbar/panels/request.html:27
msgid "No cookies"
msgstr "Немає куків"

#: templates/debug_toolbar/panels/request.html:31
msgid "Session data"
msgstr "Дані сесії"

#: templates/debug_toolbar/panels/request.html:34
msgid "No session data"
msgstr "Немає даних сесії"

#: templates/debug_toolbar/panels/request.html:38
msgid "GET data"
msgstr "GET дані"

#: templates/debug_toolbar/panels/request.html:41
msgid "No GET data"
msgstr "Немає GET даних"

#: templates/debug_toolbar/panels/request.html:45
msgid "POST data"
msgstr "POST дані"

#: templates/debug_toolbar/panels/request.html:48
msgid "No POST data"
msgstr "Немає POST даних"

#: templates/debug_toolbar/panels/settings.html:5
msgid "Setting"
msgstr "Налаштування"

#: templates/debug_toolbar/panels/signals.html:5
msgid "Signal"
msgstr "Сигнал"

#: templates/debug_toolbar/panels/signals.html:6
msgid "Receivers"
msgstr "Отримувачі сигнала"

#: templates/debug_toolbar/panels/sql.html:6
#, python-format
msgid "%(num)s query"
msgid_plural "%(num)s queries"
msgstr[0] "%(num)s запит"
msgstr[1] "%(num)s запити"
msgstr[2] "%(num)s запитів"
msgstr[3] "%(num)s запитів"

#: templates/debug_toolbar/panels/sql.html:8
#, python-format
msgid ""
"including <abbr title=\"Similar queries are queries with the same SQL, but "
"potentially different parameters.\">%(count)s similar</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:12
#, python-format
msgid ""
"and <abbr title=\"Duplicate queries are identical to each other: they "
"execute exactly the same SQL and parameters.\">%(dupes)s duplicates</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:34
msgid "Query"
msgstr "Запит"

#: templates/debug_toolbar/panels/sql.html:35
#: templates/debug_toolbar/panels/timer.html:36
msgid "Timeline"
msgstr "Лінія часу"

#: templates/debug_toolbar/panels/sql.html:52
#, python-format
msgid "%(count)s similar queries."
msgstr ""

#: templates/debug_toolbar/panels/sql.html:58
#, python-format
msgid "Duplicated %(dupes)s times."
msgstr ""

#: templates/debug_toolbar/panels/sql.html:95
msgid "Connection:"
msgstr "Підключення:"

#: templates/debug_toolbar/panels/sql.html:97
msgid "Isolation level:"
msgstr "Рівень ізоляції:"

#: templates/debug_toolbar/panels/sql.html:100
msgid "Transaction status:"
msgstr "Статус транзакції:"

#: templates/debug_toolbar/panels/sql.html:114
msgid "(unknown)"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:123
msgid "No SQL queries were recorded during this request."
msgstr "Жодного SQL запиту не було записано протягом цього запиту"

#: templates/debug_toolbar/panels/sql_explain.html:4
msgid "SQL explained"
msgstr ""

#: templates/debug_toolbar/panels/sql_explain.html:9
#: templates/debug_toolbar/panels/sql_profile.html:10
#: templates/debug_toolbar/panels/sql_select.html:9
msgid "Executed SQL"
msgstr "Виконаний SQL запит"

#: templates/debug_toolbar/panels/sql_explain.html:13
#: templates/debug_toolbar/panels/sql_profile.html:14
#: templates/debug_toolbar/panels/sql_select.html:13
msgid "Database"
msgstr "База даних"

#: templates/debug_toolbar/panels/sql_profile.html:4
msgid "SQL profiled"
msgstr ""

#: templates/debug_toolbar/panels/sql_profile.html:37
msgid "Error"
msgstr "Помилка"

#: templates/debug_toolbar/panels/sql_select.html:4
msgid "SQL selected"
msgstr ""

#: templates/debug_toolbar/panels/sql_select.html:36
msgid "Empty set"
msgstr "Порожня множина"

#: templates/debug_toolbar/panels/staticfiles.html:3
msgid "Static file path"
msgid_plural "Static file paths"
msgstr[0] "Шлях до статичних файлів"
msgstr[1] "Шляхи до статичних файлів"
msgstr[2] "Шляхи до статичних файлів"
msgstr[3] "Шляхи до статичних файлів"

#: templates/debug_toolbar/panels/staticfiles.html:7
#, python-format
msgid "(prefix %(prefix)s)"
msgstr "(префікс %(prefix)s)"

#: templates/debug_toolbar/panels/staticfiles.html:11
#: templates/debug_toolbar/panels/staticfiles.html:22
#: templates/debug_toolbar/panels/staticfiles.html:34
#: templates/debug_toolbar/panels/templates.html:10
#: templates/debug_toolbar/panels/templates.html:30
#: templates/debug_toolbar/panels/templates.html:47
msgid "None"
msgstr "Немає"

#: templates/debug_toolbar/panels/staticfiles.html:14
msgid "Static file app"
msgid_plural "Static file apps"
msgstr[0] "Застосунок, який використовує статичні файли"
msgstr[1] "Застосунки, які використовують статичні файли"
msgstr[2] "Застосунки, які використовують статичні файли"
msgstr[3] "Застосунки, які використовують статичні файли"

#: templates/debug_toolbar/panels/staticfiles.html:25
msgid "Static file"
msgid_plural "Static files"
msgstr[0] "Статичний файл"
msgstr[1] "Статичні файли"
msgstr[2] "Статичні файли"
msgstr[3] "Статичні файли"

#: templates/debug_toolbar/panels/staticfiles.html:39
#, python-format
msgid "%(payload_count)s file"
msgid_plural "%(payload_count)s files"
msgstr[0] "%(payload_count)s файл"
msgstr[1] "%(payload_count)s файли"
msgstr[2] "%(payload_count)s файлів"
msgstr[3] "%(payload_count)s файлів"

#: templates/debug_toolbar/panels/staticfiles.html:44
msgid "Location"
msgstr "Місце"

#: templates/debug_toolbar/panels/template_source.html:4
msgid "Template source:"
msgstr "Джерело шаблону:"

#: templates/debug_toolbar/panels/templates.html:2
msgid "Template path"
msgid_plural "Template paths"
msgstr[0] "Шлях до шаблонів"
msgstr[1] "Шляхи до шаблонів"
msgstr[2] "Шляхи до шаблонів"
msgstr[3] "Шляхи до шаблонів"

#: templates/debug_toolbar/panels/templates.html:13
msgid "Template"
msgid_plural "Templates"
msgstr[0] "Шаблон"
msgstr[1] "Шаблони"
msgstr[2] "Шаблонів"
msgstr[3] "Шаблонів"

#: templates/debug_toolbar/panels/templates.html:22
#: templates/debug_toolbar/panels/templates.html:40
msgid "Toggle context"
msgstr "Контекст"

#: templates/debug_toolbar/panels/templates.html:33
msgid "Context processor"
msgid_plural "Context processors"
msgstr[0] "Процесор контексту"
msgstr[1] "Процесори контексту"
msgstr[2] "Процесори контексту"
msgstr[3] "Процесори контексту"

#: templates/debug_toolbar/panels/timer.html:2
msgid "Resource usage"
msgstr "Використання ресурсів"

#: templates/debug_toolbar/panels/timer.html:10
msgid "Resource"
msgstr "Ресурс"

#: templates/debug_toolbar/panels/timer.html:26
msgid "Browser timing"
msgstr "Хронометраж браузера"

#: templates/debug_toolbar/panels/timer.html:35
msgid "Timing attribute"
msgstr "Атрибут хронометражу"

#: templates/debug_toolbar/panels/timer.html:37
msgid "Milliseconds since navigation start (+length)"
msgstr "Мілісекунд від початку навігації (+тривалість)"

#: templates/debug_toolbar/panels/versions.html:10
msgid "Package"
msgstr ""

#: templates/debug_toolbar/panels/versions.html:11
msgid "Name"
msgstr "Імʼя"

#: templates/debug_toolbar/panels/versions.html:12
msgid "Version"
msgstr "Версія"

#: templates/debug_toolbar/redirect.html:10
msgid "Location:"
msgstr "Місцезнаходження:"

#: templates/debug_toolbar/redirect.html:12
msgid ""
"The Django Debug Toolbar has intercepted a redirect to the above URL for "
"debug viewing purposes. You can click the above link to continue with the "
"redirect as normal."
msgstr "Панель Інструментів Налагодження Django перервала перенаправлення до вищевказаного URL задля налагодження перегляду. Ви можете натиснути на посилання вище, щоб продовжити перенаправлення у звичайному режимі."

#: views.py:16
msgid ""
"Data for this panel isn't available anymore. Please reload the page and "
"retry."
msgstr "Дані для цієї панелі більше недоступні. Будь ласка, перезавантажте сторінку і спробуйте знову."
