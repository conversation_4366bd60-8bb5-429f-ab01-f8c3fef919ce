{% load i18n %}

<h4>{% translate "View information" %}</h4>
<table>
  <thead>
    <tr>
      <th>{% translate "View function" %}</th>
      <th>{% translate "Arguments" %}</th>
      <th>{% translate "Keyword arguments" %}</th>
      <th>{% translate "URL name" %}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>{{ view_func }}</code></td>
      <td><code>{{ view_args|pprint }}</code></td>
      <td><code>{{ view_kwargs|pprint }}</code></td>
      <td><code>{{ view_urlname }}</code></td>
    </tr>
  </tbody>
</table>

{% if cookies.list or cookies.raw %}
  <h4>{% translate "Cookies" %}</h4>
  {% include 'debug_toolbar/panels/request_variables.html' with variables=cookies %}
{% else %}
  <h4>{% translate "No cookies" %}</h4>
{% endif %}

{% if session.list or session.raw %}
  <h4>{% translate "Session data" %}</h4>
  {% include 'debug_toolbar/panels/request_variables.html' with variables=session %}
{% else %}
  <h4>{% translate "No session data" %}</h4>
{% endif %}

{% if get.list or get.raw %}
  <h4>{% translate "GET data" %}</h4>
  {% include 'debug_toolbar/panels/request_variables.html' with variables=get %}
{% else %}
  <h4>{% translate "No GET data" %}</h4>
{% endif %}

{% if post.list or post.raw %}
  <h4>{% translate "POST data" %}</h4>
  {% include 'debug_toolbar/panels/request_variables.html' with variables=post %}
{% else %}
  <h4>{% translate "No POST data" %}</h4>
{% endif %}
