/* Dashboard Components - Cards, Metrics, Charts */

/* Card Components */
.card {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--space-lg);
    overflow: hidden;
    transition: var(--transition-base);
    border: 1px solid var(--border-color);
}

.card:hover {
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: var(--card-bg);
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-main);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.card-title i {
    color: var(--primary);
    font-size: 1.2em;
}

.card-body {
    padding: var(--space-lg);
}

.card-body.p-0 {
    padding: 0;
}

/* Metric Cards */
.metric-card {
    background: var(--card-bg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: var(--space-md) var(--space-lg);
    transition: var(--transition-base);
    display: flex;
    align-items: flex-start;
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: var(--accent-color, var(--primary));
    transition: var(--transition-base);
    opacity: 0;
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
}

.metric-card:hover::before {
    opacity: 1;
}

/* Metric Card Accent Colors */
.metric-card.accent-primary { --accent-color: var(--primary); }
.metric-card.accent-success { --accent-color: var(--success); }
.metric-card.accent-danger { --accent-color: var(--danger); }
.metric-card.accent-warning { --accent-color: var(--warning); }
.metric-card.accent-info { --accent-color: var(--info); }
.metric-card.accent-purple { --accent-color: var(--purple); }

.metric-icon {
    width: 52px;
    height: 52px;
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--space-md);
    flex-shrink: 0;
}

.metric-icon i {
    font-size: 1.75rem;
}

.metric-content {
    flex: 1;
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-bottom: var(--space-xs);
    display: block;
    font-weight: 500;
}

.metric-value {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-main);
    margin-bottom: var(--space-sm);
    display: flex;
    align-items: baseline;
    line-height: 1.2;
}

.metric-value .unit {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-left: var(--space-xs);
    font-weight: 500;
}

.metric-comparison {
    font-size: var(--font-size-xs);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--text-muted);
}

.trend-up { color: var(--success); }
.trend-down { color: var(--danger); }
.trend-neutral { color: var(--text-muted); }

.metric-comparison i {
    font-size: 1rem;
    position: relative;
    top: 1px;
}

/* Icon Background Colors */
.icon-primary { background-color: var(--primary-alpha); color: var(--primary); }
.icon-success { background-color: var(--success-alpha); color: var(--success); }
.icon-danger { background-color: var(--danger-alpha); color: var(--danger); }
.icon-warning { background-color: var(--warning-alpha); color: var(--warning); }
.icon-info { background-color: var(--info-alpha); color: var(--info); }
.icon-purple { background-color: var(--purple-alpha); color: var(--purple); }

/* Charts */
.chart-container {
    position: relative;
    height: 320px;
    width: 100%;
}

.chart-container-doughnut {
    height: 280px;
    max-width: 280px;
    margin: 0 auto;
}

/* Tab Controls */
.tab-controls {
    display: inline-flex;
    background: var(--bg-main);
    border-radius: var(--border-radius-sm);
    padding: var(--space-xs);
    border: 1px solid var(--border-color);
}

.tab-control {
    border: none;
    background: none;
    padding: var(--space-sm) var(--space-md);
    font-size: var(--font-size-sm);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    color: var(--text-muted);
    font-weight: 500;
}

.tab-control.active {
    background: var(--card-bg);
    color: var(--primary);
    box-shadow: var(--shadow-sm);
}

.tab-control:hover:not(.active) {
    color: var(--text-main);
    background: var(--light-gray);
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: var(--font-size-sm);
}

.data-table th,
.data-table td {
    padding: var(--space-md) var(--space-lg);
    text-align: left;
    vertical-align: middle;
    color: var(--text-main);
    white-space: nowrap;
}

.data-table thead th {
    background: var(--light-gray);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--text-muted);
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table thead th:first-child {
    border-top-left-radius: var(--border-radius-lg);
}

.data-table thead th:last-child {
    border-top-right-radius: var(--border-radius-lg);
}

.data-table tbody tr {
    transition: background-color var(--transition-fast);
}

.data-table tbody tr:hover {
    background-color: var(--light-gray);
}

.data-table tbody tr:not(:last-child) {
    border-bottom: 1px solid var(--border-color);
}

.data-table tbody td:first-child {
    border-left: 3px solid transparent;
    transition: border-color var(--transition-fast);
}

.data-table tbody tr:hover td:first-child {
    border-left-color: var(--primary);
}

/* Case Number Links */
.case-number {
    font-weight: 600;
    color: var(--primary);
    text-decoration: none;
}

.case-number:hover {
    text-decoration: underline;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-weight: 500;
    font-size: var(--font-size-xs);
    line-height: 1;
    white-space: nowrap;
    text-transform: capitalize;
}

.status-badge i {
    margin-right: 0.35rem;
    font-size: 0.7rem;
    position: relative;
    top: -1px;
}

.status-pending_acceptance { background-color: var(--warning-alpha); color: var(--warning); }
.status-in_progress { background-color: var(--info-alpha); color: var(--info); }
.status-on_hold { background-color: hsla(210, 10%, 50%, 0.2); color: var(--gray); }
.status-ready_to_ship { background-color: var(--purple-alpha); color: var(--purple); }
.status-shipped { background-color: var(--success-alpha); color: var(--success); }
.status-delivered { background-color: var(--success-alpha); color: var(--success); }
.status-closed { background-color: hsla(210, 10%, 50%, 0.15); color: var(--text-muted); }
.status-default { background-color: hsla(0, 0%, 50%, 0.1); color: var(--text-muted); }

/* Dentist Info */
.dentist-info {
    display: flex;
    align-items: center;
}

.avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: var(--white);
    background-color: var(--primary);
    font-size: var(--font-size-sm);
    margin-right: var(--space-md);
    flex-shrink: 0;
    text-transform: uppercase;
}

/* Avatar Color Variations */
.avatar-A, .avatar-G, .avatar-M, .avatar-S, .avatar-Y { background-color: var(--success); }
.avatar-B, .avatar-H, .avatar-N, .avatar-T, .avatar-Z { background-color: var(--info); }
.avatar-C, .avatar-I, .avatar-O, .avatar-U { background-color: var(--warning); color: var(--dark-light); }
.avatar-D, .avatar-J, .avatar-P, .avatar-V { background-color: var(--danger); }
.avatar-E, .avatar-K, .avatar-Q, .avatar-W { background-color: var(--purple); }

.dentist-details {
    display: flex;
    flex-direction: column;
    line-height: 1.3;
}

.dentist-name {
    font-weight: 500;
    color: var(--text-main);
}

.dentist-clinic {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.actions-group {
    display: flex;
    gap: var(--space-sm);
    align-items: center;
}

/* Empty State */
.empty-state {
    padding: var(--space-xxl) var(--space-xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    text-align: center;
}

.empty-state i {
    font-size: 3.5rem;
    margin-bottom: var(--space-md);
    opacity: 0.4;
    color: var(--gray);
}

.empty-state-text {
    font-size: var(--font-size-base);
    font-weight: 500;
    margin: 0;
}

/* Responsive Component Adjustments */
@media (max-width: 767.98px) {
    .metric-card {
        padding: var(--space-md);
    }
    
    .metric-icon {
        width: 44px;
        height: 44px;
    }
    
    .metric-value {
        font-size: 1.625rem;
    }
    
    .tab-controls {
        width: 100%;
        display: flex;
    }
    
    .tab-control {
        flex: 1;
        text-align: center;
    }
    
    .chart-container {
        height: 280px;
    }
    
    .chart-container-doughnut {
        height: 240px;
        max-width: 240px;
    }
    
    .data-table th,
    .data-table td {
        padding: var(--space-sm) var(--space-md);
    }
}
