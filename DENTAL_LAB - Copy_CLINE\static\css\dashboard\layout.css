/* Dashboard Layout Components */

.page-container {
    padding: var(--space-lg) var(--space-xl);
    max-width: 1600px;
    margin: 0 auto;
    min-height: 100vh;
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
    gap: var(--space-md);
}

.dashboard-title-wrapper {
    flex-basis: 100%;
}

@media (min-width: 992px) {
    .dashboard-title-wrapper {
        flex-basis: auto;
    }
}

.dashboard-title {
    margin: 0;
    font-weight: 700;
    font-size: var(--font-size-3xl);
    color: var(--text-main);
    position: relative;
    padding-bottom: var(--space-sm);
}

.dashboard-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 4px;
    background: var(--primary);
    border-radius: 2px;
}

.dashboard-subtitle {
    color: var(--text-muted);
    margin-top: var(--space-xs);
    font-size: var(--font-size-base);
    font-weight: 400;
}

.dashboard-actions {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-md);
    align-items: center;
    flex-grow: 1;
    justify-content: flex-end;
}

/* Action Controls */
.input-group-wrapper {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    background: var(--card-bg);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.input-group-wrapper:focus-within {
    box-shadow: 0 0 0 2px var(--primary-alpha);
    border-color: var(--primary);
}

.input-group-wrapper i {
    color: var(--primary);
    font-size: 1.1rem;
}

.input-control {
    border: none;
    background: transparent;
    font-size: var(--font-size-sm);
    color: var(--text-main);
    padding: var(--space-xs);
    outline: none;
    flex-grow: 1;
}

.search-input {
    min-width: 200px;
}

.input-control::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

.action-buttons {
    display: flex;
    gap: var(--space-sm);
}

.btn-action {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: var(--card-bg);
    color: var(--text-muted);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    font-size: 1.1rem;
}

.btn-action:hover,
.btn-action:focus {
    background: var(--primary);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary);
}

/* Grid Layout */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

/* Bootstrap-like Grid */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-left: calc(var(--space-lg) / -2);
    margin-right: calc(var(--space-lg) / -2);
}

.col-12,
.col-lg-4,
.col-lg-8 {
    width: 100%;
    padding-left: calc(var(--space-lg) / 2);
    padding-right: calc(var(--space-lg) / 2);
}

@media (min-width: 992px) {
    .col-lg-4 {
        flex: 0 0 33.3333%;
        max-width: 33.3333%;
    }
    
    .col-lg-8 {
        flex: 0 0 66.6667%;
        max-width: 66.6667%;
    }
}

/* Responsive Layout */
@media (max-width: 991.98px) {
    .page-container {
        padding: var(--space-md) var(--space-lg);
    }
    
    .metrics-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: var(--space-md);
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .dashboard-actions {
        width: 100%;
        justify-content: flex-start;
        margin-top: var(--space-md);
    }
    
    .search-input {
        min-width: 150px;
    }
}

@media (max-width: 767.98px) {
    .metrics-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .search-input {
        min-width: 0;
        width: 100%;
    }
    
    .input-group-wrapper {
        width: 100%;
    }
    
    .dashboard-actions {
        gap: var(--space-sm);
    }
    
    .action-buttons {
        margin-left: auto;
    }
}

@media (max-width: 575.98px) {
    .page-container {
        padding: var(--space-sm) var(--space-md);
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-title {
        font-size: 1.6rem;
    }
    
    .dashboard-subtitle {
        font-size: 0.9rem;
    }
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Error States */
.error-state {
    text-align: center;
    padding: var(--space-xl);
    color: var(--text-muted);
}

.error-state i {
    font-size: 3rem;
    margin-bottom: var(--space-md);
    color: var(--danger);
}

/* Utility Classes */
.mb-4 { margin-bottom: var(--space-lg); }
.me-2 { margin-right: var(--space-sm); }
.p-0 { padding: 0 !important; }
.fw-semibold { font-weight: 600 !important; }
.text-center { text-align: center; }
.d-flex { display: flex !important; }
.align-items-center { align-items: center !important; }
.justify-content-center { justify-content: center !important; }
.h-100 { height: 100% !important; }

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    z-index: 1000;
    border-radius: 4px;
}

.skip-link:focus {
    top: 6px;
}
