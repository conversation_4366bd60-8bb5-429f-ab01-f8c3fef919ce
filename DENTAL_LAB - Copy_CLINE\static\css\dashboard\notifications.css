/* Notifications, Banners, and Alerts */

/* Notifications */
.notification-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
    margin: 0;
    list-style: none;
}

.notification-item {
    display: flex;
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid var(--border-color);
    transition: background-color var(--transition-fast);
    background-color: var(--card-bg);
    gap: var(--space-md);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item:hover {
    background-color: var(--light-gray);
}

.notification-important {
    border-left: 4px solid var(--primary);
    padding-left: calc(var(--space-lg) - 4px);
}

.notification-urgent {
    border-left: 4px solid var(--danger);
    padding-left: calc(var(--space-lg) - 4px);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: var(--text-muted);
}

.notification-icon i {
    font-size: 1.2rem;
}

.notification-important .notification-icon {
    background-color: var(--primary-alpha);
    color: var(--primary);
}

.notification-urgent .notification-icon {
    background-color: var(--danger-alpha);
    color: var(--danger);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--text-main);
    margin-bottom: var(--space-xs);
    font-size: 0.9375rem;
}

.notification-text {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-bottom: var(--space-xs);
    line-height: 1.5;
}

.notification-time {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    opacity: 0.8;
}

.notification-actions {
    display: flex;
    align-items: center;
    margin-left: auto;
    gap: var(--space-sm);
}

.notification-actions .btn {
    font-size: 0.8rem;
    padding: 0.3rem 0.7rem;
}

/* Ready to Ship Banner */
.ready-to-ship-banner {
    background: linear-gradient(105deg, var(--success-alpha) 0%, var(--card-bg) 70%);
    border-radius: var(--border-radius-lg);
    padding: var(--space-lg);
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    border-left: 5px solid var(--success);
}

.banner-content {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    flex-grow: 1;
}

.banner-icon {
    width: 60px;
    height: 60px;
    background: var(--card-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: var(--success);
    font-size: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.banner-text h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin: 0 0 var(--space-xs) 0;
    color: var(--text-main);
}

.banner-text p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9375rem;
    line-height: 1.5;
}

.banner-text strong {
    color: var(--text-main);
    font-weight: 600;
}

.banner-action {
    margin-left: var(--space-md);
    margin-top: var(--space-sm);
}

.banner-action .btn-ship {
    background: var(--success);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 0.625rem 1.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    box-shadow: var(--shadow-sm);
}

.banner-action .btn-ship:hover {
    background: hsl(var(--success-hue), 53%, 40%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.banner-action .btn-ship i {
    font-size: 1em;
}

/* Alert Components */
.alert {
    padding: var(--space-md);
    margin-bottom: var(--space-lg);
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    position: relative;
}

.alert i {
    font-size: 1.2em;
    flex-shrink: 0;
}

.alert-danger {
    color: var(--danger);
    background-color: var(--danger-alpha);
    border-color: var(--danger);
}

.alert-success {
    color: var(--success);
    background-color: var(--success-alpha);
    border-color: var(--success);
}

.alert-warning {
    color: var(--warning);
    background-color: var(--warning-alpha);
    border-color: var(--warning);
}

.alert-info {
    color: var(--info);
    background-color: var(--info-alpha);
    border-color: var(--info);
}

.alert-dismissible {
    padding-right: 3rem;
}

.alert-close {
    position: absolute;
    top: 0;
    right: 0;
    padding: var(--space-md);
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    transition: opacity var(--transition-fast);
}

.alert-close:hover {
    opacity: 1;
}

/* Button Components */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.btn:focus {
    outline: 2px solid transparent;
    box-shadow: 0 0 0 2px var(--primary-alpha);
}

.btn-primary {
    background-color: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: hsl(var(--primary-hue), 90%, 50%);
    border-color: hsl(var(--primary-hue), 90%, 50%);
}

.btn-outline-secondary {
    background-color: transparent;
    color: var(--text-muted);
    border-color: var(--border-color);
}

.btn-outline-secondary:hover {
    background-color: var(--light-gray);
    color: var(--text-main);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: var(--font-size-lg);
}

/* Loading Skeleton */
.skeleton {
    background: linear-gradient(
        90deg,
        var(--light-gray) 25%,
        var(--card-bg) 50%,
        var(--light-gray) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-text {
    height: 1rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.skeleton-text:last-child {
    margin-bottom: 0;
    width: 60%;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.skeleton-card {
    height: 200px;
    border-radius: var(--border-radius-md);
}

/* Responsive Adjustments */
@media (max-width: 991.98px) {
    .banner-content {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-md);
    }
    
    .banner-icon {
        margin-right: 0;
    }
    
    .ready-to-ship-banner {
        flex-direction: column;
        align-items: stretch;
    }
    
    .banner-action {
        align-self: flex-end;
        margin-left: 0;
        margin-top: var(--space-md);
    }
    
    .notification-item {
        flex-wrap: wrap;
    }
    
    .notification-actions {
        margin-left: 0;
        margin-top: var(--space-sm);
        width: 100%;
        justify-content: flex-end;
    }
}

@media (max-width: 575.98px) {
    .banner-text h3 {
        font-size: 1.1rem;
    }
    
    .banner-text p {
        font-size: var(--font-size-sm);
    }
    
    .banner-action .btn-ship {
        width: 100%;
        text-align: center;
        justify-content: center;
    }
    
    .alert {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }
    
    .alert i {
        margin-bottom: var(--space-xs);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .alert {
        border-width: 2px;
    }
    
    .notification-important,
    .notification-urgent {
        border-left-width: 6px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .skeleton {
        animation: none;
        background: var(--light-gray);
    }
    
    .btn-ship:hover {
        transform: none;
    }
}
