/* Dashboard Color System and CSS Variables */

:root {
    /* Color Hues */
    --primary-hue: 211;
    --success-hue: 145;
    --danger-hue: 5;
    --warning-hue: 45;
    --info-hue: 185;
    --purple-hue: 270;

    /* Light Mode Colors */
    --primary-light: hsl(var(--primary-hue), 90%, 58%);
    --success-light: hsl(var(--success-hue), 53%, 48%);
    --danger-light: hsl(var(--danger-hue), 80%, 54%);
    --warning-light: hsl(var(--warning-hue), 96%, 50%);
    --info-light: hsl(var(--info-hue), 56%, 52%);
    --purple-light: hsl(var(--purple-hue), 88%, 64%);
    --dark-light: hsl(210, 5%, 25%);
    --gray-light: hsl(210, 5%, 40%);
    --light-gray-light: hsl(210, 17%, 98%);
    --white-light: #ffffff;
    --border-light: hsla(0, 0%, 0%, 0.08);
    --shadow-color-light: 220, 3%, 15%;
    --bg-main-light: hsl(216, 33%, 97%);
    --card-bg-light: var(--white-light);

    /* Dark Mode Colors */
    --primary-dark: hsl(var(--primary-hue), 85%, 75%);
    --success-dark: hsl(var(--success-hue), 45%, 65%);
    --danger-dark: hsl(var(--danger-hue), 85%, 75%);
    --warning-dark: hsl(var(--warning-hue), 95%, 70%);
    --info-dark: hsl(var(--info-hue), 70%, 70%);
    --purple-dark: hsl(var(--purple-hue), 85%, 75%);
    --dark-dark: hsl(210, 15%, 90%);
    --gray-dark: hsl(210, 8%, 65%);
    --light-gray-dark: hsl(210, 5%, 25%);
    --white-dark: hsl(220, 4%, 13%);
    --border-dark: hsla(0, 0%, 100%, 0.08);
    --shadow-color-dark: 220, 40%, 2%;
    --bg-main-dark: var(--white-dark);
    --card-bg-dark: hsl(220, 3%, 16%);

    /* Active Theme (Default: Light) */
    --primary: var(--primary-light);
    --success: var(--success-light);
    --danger: var(--danger-light);
    --warning: var(--warning-light);
    --info: var(--info-light);
    --purple: var(--purple-light);
    --dark: var(--dark-light);
    --gray: var(--gray-light);
    --light-gray: var(--light-gray-light);
    --white: var(--white-light);
    --bg-main: var(--bg-main-light);
    --text-main: var(--dark-light);
    --text-muted: var(--gray-light);
    --card-bg: var(--card-bg-light);
    --border-color: var(--border-light);
    --shadow-color: var(--shadow-color-light);

    /* Alpha Colors for Light Mode */
    --primary-alpha: hsla(var(--primary-hue), 90%, 58%, 0.1);
    --success-alpha: hsla(var(--success-hue), 53%, 48%, 0.1);
    --danger-alpha: hsla(var(--danger-hue), 80%, 54%, 0.1);
    --warning-alpha: hsla(var(--warning-hue), 96%, 50%, 0.1);
    --info-alpha: hsla(var(--info-hue), 56%, 52%, 0.1);
    --purple-alpha: hsla(var(--purple-hue), 88%, 64%, 0.1);

    /* Spacing & Sizing */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-xxl: 3rem;      /* 48px */

    /* Border Radius */
    --border-radius-sm: 6px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;

    /* Shadows */
    --shadow-sm: 0 1px 2px hsla(var(--shadow-color), 0.05), 
                 0 1px 3px hsla(var(--shadow-color), 0.1);
    --shadow-md: 0 4px 6px -1px hsla(var(--shadow-color), 0.1), 
                 0 2px 4px -1px hsla(var(--shadow-color), 0.06);
    --shadow-lg: 0 10px 15px -3px hsla(var(--shadow-color), 0.1), 
                 0 4px 6px -2px hsla(var(--shadow-color), 0.05);
    --shadow-xl: 0 20px 25px -5px hsla(var(--shadow-color), 0.1), 
                 0 10px 10px -5px hsla(var(--shadow-color), 0.04);

    /* Transitions */
    --transition-fast: all 0.2s ease-in-out;
    --transition-base: all 0.3s ease-in-out;

    /* Typography */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 
                 Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
}

/* Dark Mode Theme Toggle */
[data-theme="dark"] {
    --primary: var(--primary-dark);
    --success: var(--success-dark);
    --danger: var(--danger-dark);
    --warning: var(--warning-dark);
    --info: var(--info-dark);
    --purple: var(--purple-dark);
    --dark: var(--dark-dark);
    --gray: var(--gray-dark);
    --light-gray: var(--light-gray-dark);
    --white: var(--white-dark);
    --bg-main: var(--bg-main-dark);
    --text-main: var(--dark-dark);
    --text-muted: var(--gray-dark);
    --card-bg: var(--card-bg-dark);
    --border-color: var(--border-dark);
    --shadow-color: var(--shadow-color-dark);

    --primary-alpha: hsla(var(--primary-hue), 85%, 75%, 0.15);
    --success-alpha: hsla(var(--success-hue), 45%, 65%, 0.15);
    --danger-alpha: hsla(var(--danger-hue), 85%, 75%, 0.15);
    --warning-alpha: hsla(var(--warning-hue), 95%, 70%, 0.15);
    --info-alpha: hsla(var(--info-hue), 70%, 70%, 0.15);
    --purple-alpha: hsla(var(--purple-hue), 85%, 75%, 0.15);
}

/* Global Reset and Base Styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    background-color: var(--bg-main);
    font-family: var(--font-sans);
    color: var(--text-main);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color var(--transition-base), color var(--transition-base);
}

/* Focus Management */
*:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

*:focus:not(:focus-visible) {
    outline: none;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --border-color: currentColor;
        --shadow-color: 0, 0%, 0%;
    }
}
