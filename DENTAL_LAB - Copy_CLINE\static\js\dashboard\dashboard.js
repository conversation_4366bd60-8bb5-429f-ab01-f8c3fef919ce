/**
 * Dashboard JavaScript - Modern, Modular Implementation
 * Handles charts, interactions, and real-time updates
 */

class DashboardManager {
    constructor() {
        this.charts = {};
        this.config = {
            defaultColors: [
                '#4285F4', '#34A853', '#EA4335', '#FBBC05',
                '#46bdc6', '#a142f4', '#ff6d01', '#0d7377'
            ],
            chartOptions: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            boxWidth: 8
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        };
        this.init();
    }

    init() {
        this.setupChartDefaults();
        this.initializeCharts();
        this.setupEventListeners();
        this.initializeTheme();
        
        console.log('Dashboard initialized successfully');
    }

    setupChartDefaults() {
        if (typeof Chart !== 'undefined') {
            Chart.defaults.font.family = "'Inter', sans-serif";
            Chart.defaults.font.size = 12;
            Chart.defaults.color = getComputedStyle(document.documentElement)
                .getPropertyValue('--text-muted').trim();
        }
    }

    initializeCharts() {
        this.initTrendChart();
        this.initStatusChart();
        this.initPriorityChart();
        this.initDepartmentChart();
    }

    initTrendChart() {
        const canvas = document.getElementById('trendChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        try {
            // Get data from template context
            const trend30Days = window.trend30DaysData || [];
            const trend12Months = window.trend12MonthsData || [];
            const trend24Months = window.trend24MonthsData || [];
            const trendAllTime = window.trendAllTimeData || [];

            // Format data for Chart.js
            const formatted30Days = this.formatTrendData(trend30Days, 'day');
            const formatted12Months = this.formatTrendData(trend12Months, 'month');
            const formatted24Months = this.formatTrendData(trend24Months, 'month');
            const formattedAllTime = this.formatTrendData(trendAllTime, 'month');

            // Create chart with 30-day data as default
            this.charts.trend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: formatted30Days.labels,
                    datasets: [{
                        label: 'Cases',
                        data: formatted30Days.data,
                        borderColor: this.config.defaultColors[0],
                        backgroundColor: this.config.defaultColors[0] + '20',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        pointBackgroundColor: '#ffffff',
                        pointBorderWidth: 2
                    }]
                },
                options: {
                    ...this.config.chartOptions,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: getComputedStyle(document.documentElement)
                                    .getPropertyValue('--border-color').trim()
                            },
                            ticks: {
                                stepSize: 1
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        ...this.config.chartOptions.plugins,
                        tooltip: {
                            backgroundColor: 'rgba(0,0,0,0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: this.config.defaultColors[0],
                            borderWidth: 1
                        }
                    }
                }
            });

            // Setup tab controls
            this.setupTrendTabs(formatted30Days, formatted12Months, formatted24Months, formattedAllTime);

        } catch (error) {
            console.error('Error initializing trend chart:', error);
            this.showChartError(canvas.parentNode, 'Error loading trend data');
        }
    }

    formatTrendData(data, type) {
        if (!Array.isArray(data) || data.length === 0) {
            return { labels: [], data: [] };
        }

        const labels = data.map(item => {
            if (type === 'day') {
                return new Date(item.date).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric'
                });
            } else {
                return new Date(item.month).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short'
                });
            }
        });

        const values = data.map(item => item.count || 0);

        return { labels, data: values };
    }

    setupTrendTabs(data30Days, data12Months, data24Months, dataAllTime) {
        const tabs = {
            'trend30DaysBtn': data30Days,
            'trend12MonthsBtn': data12Months,
            'trend24MonthsBtn': data24Months,
            'trendAllTimeBtn': dataAllTime
        };

        Object.entries(tabs).forEach(([btnId, data]) => {
            const btn = document.getElementById(btnId);
            if (btn) {
                btn.addEventListener('click', () => {
                    this.updateTrendChart(data);
                    this.setActiveTab(btn);
                });
            }
        });
    }

    updateTrendChart(data) {
        if (!this.charts.trend || !data) return;

        this.charts.trend.data.labels = data.labels;
        this.charts.trend.data.datasets[0].data = data.data;
        this.charts.trend.update('active');
    }

    setActiveTab(activeBtn) {
        document.querySelectorAll('.tab-control').forEach(btn => {
            btn.classList.remove('active');
            btn.setAttribute('aria-pressed', 'false');
        });
        
        activeBtn.classList.add('active');
        activeBtn.setAttribute('aria-pressed', 'true');
    }

    initStatusChart() {
        const canvas = document.getElementById('statusChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        try {
            const statusData = window.statusChartData || [];
            
            if (statusData.length === 0) {
                this.showChartError(canvas.parentNode, 'No status data available');
                return;
            }

            const labels = statusData.map(item => 
                item.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
            );
            const data = statusData.map(item => item.total);

            this.charts.status = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: this.config.defaultColors,
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    ...this.config.chartOptions,
                    cutout: '60%',
                    plugins: {
                        ...this.config.chartOptions.plugins,
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                boxWidth: 12
                            }
                        }
                    }
                }
            });

        } catch (error) {
            console.error('Error initializing status chart:', error);
            this.showChartError(canvas.parentNode, 'Error loading status data');
        }
    }

    initPriorityChart() {
        const canvas = document.getElementById('priorityChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        try {
            const priorityData = window.priorityChartData || [];
            
            if (priorityData.length === 0) {
                this.showChartError(canvas.parentNode, 'No priority data available');
                return;
            }

            const priorityLabels = ['High', 'Normal', 'Low'];
            const labels = priorityData.map(item => priorityLabels[item.priority - 1] || 'Unknown');
            const data = priorityData.map(item => item.total);

            this.charts.priority = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            this.config.defaultColors[2], // High - Red
                            this.config.defaultColors[0], // Normal - Blue
                            this.config.defaultColors[1]  // Low - Green
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    ...this.config.chartOptions,
                    cutout: '60%'
                }
            });

        } catch (error) {
            console.error('Error initializing priority chart:', error);
            this.showChartError(canvas.parentNode, 'Error loading priority data');
        }
    }

    initDepartmentChart() {
        const canvas = document.getElementById('departmentChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        try {
            const departmentData = window.departmentChartData || [];
            
            if (departmentData.length === 0) {
                this.showChartError(canvas.parentNode, 'No department data available');
                return;
            }

            const labels = departmentData.map(item => item.responsible_department__name || 'Unassigned');
            const data = departmentData.map(item => item.total_cases);

            this.charts.department = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Cases',
                        data: data,
                        backgroundColor: this.config.defaultColors[4] + '80',
                        borderColor: this.config.defaultColors[4],
                        borderWidth: 1
                    }]
                },
                options: {
                    ...this.config.chartOptions,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });

        } catch (error) {
            console.error('Error initializing department chart:', error);
            this.showChartError(canvas.parentNode, 'Error loading department data');
        }
    }

    showChartError(container, message) {
        if (container) {
            container.innerHTML = `
                <div class="empty-state text-center">
                    <i class="bi bi-exclamation-triangle"></i>
                    <p class="empty-state-text">${message}</p>
                </div>
            `;
        }
    }

    setupEventListeners() {
        this.setupDateRangeFilter();
        this.setupSearch();
        this.setupThemeToggle();
        this.setupRefreshButton();
    }

    setupDateRangeFilter() {
        const filterElement = document.getElementById('dateRangeFilter');
        if (filterElement) {
            filterElement.addEventListener('change', (e) => {
                this.handleDateRangeChange(e.target.value);
            });
        }
    }

    handleDateRangeChange(days) {
        try {
            const baseUrl = window.location.pathname;
            const url = new URL(baseUrl, window.location.origin);
            url.searchParams.set('range', days);
            
            // Show loading state
            this.showLoadingState();
            
            window.location.href = url.toString();
        } catch (error) {
            console.error('Error changing date range:', error);
            window.location.reload();
        }
    }

    setupSearch() {
        const searchInput = document.getElementById('globalSearch');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch();
                }
            });
        }

        // Setup search button
        const searchButton = document.querySelector('[onclick="performSearch()"]');
        if (searchButton) {
            searchButton.onclick = () => this.performSearch();
        }
    }

    performSearch() {
        const searchInput = document.getElementById('globalSearch');
        const searchTerm = searchInput?.value.trim();
        
        if (searchTerm) {
            const searchUrl = window.searchCasesUrl || '/case/search/';
            window.location.href = `${searchUrl}?q=${encodeURIComponent(searchTerm)}`;
        }
    }

    setupThemeToggle() {
        const themeToggle = document.getElementById('darkModeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    initializeTheme() {
        const savedTheme = localStorage.getItem('dashboard-theme') || 'light';
        this.setTheme(savedTheme);
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('dashboard-theme', theme);
        
        const themeToggle = document.getElementById('darkModeToggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = theme === 'dark' ? 'bi bi-sun' : 'bi bi-moon-stars';
            }
        }

        // Update chart colors if charts exist
        this.updateChartTheme();
    }

    updateChartTheme() {
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.options) {
                const textColor = getComputedStyle(document.documentElement)
                    .getPropertyValue('--text-muted').trim();
                
                Chart.defaults.color = textColor;
                chart.update('none');
            }
        });
    }

    setupRefreshButton() {
        const refreshButton = document.querySelector('[onclick="window.location.reload();"]');
        if (refreshButton) {
            refreshButton.onclick = () => {
                this.showLoadingState();
                window.location.reload();
            };
        }
    }

    showLoadingState() {
        document.querySelectorAll('.metric-card').forEach(card => {
            card.classList.add('loading');
        });
    }

    // Public API for external use
    refresh() {
        this.showLoadingState();
        window.location.reload();
    }

    updateMetrics(metricsData) {
        // Update metric cards with new data
        Object.entries(metricsData).forEach(([key, value]) => {
            const element = document.querySelector(`[data-metric="${key}"]`);
            if (element) {
                element.textContent = value;
            }
        });
    }

    destroy() {
        // Clean up charts and event listeners
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
        this.charts = {};
    }
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.dashboardManager = new DashboardManager();
});

// Expose global functions for backward compatibility
window.performSearch = () => {
    if (window.dashboardManager) {
        window.dashboardManager.performSearch();
    }
};
