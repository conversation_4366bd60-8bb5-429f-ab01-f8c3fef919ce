/**
 * Enhanced Dashboard JavaScript with Financial Metrics
 * Handles dual-axis charts, currency conversion, and enhanced visualizations
 */

class EnhancedDashboardManager {
    constructor() {
        this.charts = {};
        this.currentCurrency = window.currentCurrency || 'USD';
        this.chartMode = 'volume'; // volume, revenue, dual
        this.exchangeRates = {};
        
        this.config = {
            defaultColors: [
                '#4285F4', '#34A853', '#EA4335', '#FBBC05',
                '#46bdc6', '#a142f4', '#ff6d01', '#0d7377'
            ],
            currencyColors: {
                primary: '#34A853',   // Green for revenue
                secondary: '#4285F4'  // Blue for volume
            },
            chartOptions: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            boxWidth: 8
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        };
        
        this.init();
    }

    init() {
        this.setupChartDefaults();
        this.initializeEnhancedCharts();
        this.setupEventListeners();
        this.initializeTheme();
        this.loadExchangeRates();
        
        console.log('Enhanced Dashboard initialized successfully');
    }

    setupChartDefaults() {
        if (typeof Chart !== 'undefined') {
            Chart.defaults.font.family = "'Inter', sans-serif";
            Chart.defaults.font.size = 12;
            Chart.defaults.color = getComputedStyle(document.documentElement)
                .getPropertyValue('--text-muted').trim();
        }
    }

    initializeEnhancedCharts() {
        this.initEnhancedTrendChart();
        this.initRevenueChart();
    }

    initEnhancedTrendChart() {
        const canvas = document.getElementById('enhancedTrendChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        try {
            // Get enhanced data from template context
            const trend30Days = window.trend30DaysData || [];
            const trend12Months = window.trend12MonthsData || [];
            const trend24Months = window.trend24MonthsData || [];
            const trendAllTime = window.trendAllTimeData || [];

            // Format data for Chart.js with enhanced metrics
            const formatted30Days = this.formatEnhancedTrendData(trend30Days, 'day');
            const formatted12Months = this.formatEnhancedTrendData(trend12Months, 'month');
            const formatted24Months = this.formatEnhancedTrendData(trend24Months, 'month');
            const formattedAllTime = this.formatEnhancedTrendData(trendAllTime, 'month');

            // Create enhanced chart based on current mode
            this.createEnhancedChart(ctx, formatted30Days);

            // Setup enhanced tab controls
            this.setupEnhancedTrendTabs(formatted30Days, formatted12Months, formatted24Months, formattedAllTime);

        } catch (error) {
            console.error('Error initializing enhanced trend chart:', error);
            this.showChartError(canvas.parentNode, 'Error loading enhanced trend data');
        }
    }

    formatEnhancedTrendData(data, type) {
        if (!Array.isArray(data) || data.length === 0) {
            return { 
                labels: [], 
                volumeData: [], 
                revenueData: [],
                combinedData: []
            };
        }

        const labels = data.map(item => {
            if (type === 'day') {
                return item.label || new Date(item.date).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric'
                });
            } else {
                return item.label || new Date(item.month).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short'
                });
            }
        });

        const volumeData = data.map(item => item.count || 0);
        const revenueData = data.map(item => item.value || 0);

        return { 
            labels, 
            volumeData, 
            revenueData,
            rawData: data
        };
    }

    createEnhancedChart(ctx, data) {
        // Destroy existing chart
        if (this.charts.enhancedTrend) {
            this.charts.enhancedTrend.destroy();
        }

        const datasets = this.getDatasetsByMode(data);
        const scales = this.getScalesByMode();

        this.charts.enhancedTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: datasets
            },
            options: {
                ...this.config.chartOptions,
                scales: scales,
                plugins: {
                    ...this.config.chartOptions.plugins,
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: this.config.defaultColors[0],
                        borderWidth: 1,
                        callbacks: {
                            afterBody: (context) => {
                                if (this.chartMode === 'dual' && context.length > 0) {
                                    const dataIndex = context[0].dataIndex;
                                    const rawData = data.rawData[dataIndex];
                                    if (rawData) {
                                        return [
                                            `Cases: ${rawData.count || 0}`,
                                            `Revenue: ${rawData.value_formatted || '$0.00'}`,
                                            `Avg/Case: ${rawData.avg_case_value ? this.formatCurrency(rawData.avg_case_value) : '$0.00'}`
                                        ];
                                    }
                                }
                                return [];
                            }
                        }
                    }
                }
            }
        });
    }

    getDatasetsByMode(data) {
        switch (this.chartMode) {
            case 'revenue':
                return [{
                    label: 'Revenue',
                    data: data.revenueData,
                    borderColor: this.config.currencyColors.primary,
                    backgroundColor: this.config.currencyColors.primary + '20',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    pointBackgroundColor: '#ffffff',
                    pointBorderWidth: 2,
                    yAxisID: 'y'
                }];
                
            case 'dual':
                return [
                    {
                        label: 'Cases',
                        data: data.volumeData,
                        borderColor: this.config.currencyColors.secondary,
                        backgroundColor: this.config.currencyColors.secondary + '20',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        pointBackgroundColor: '#ffffff',
                        pointBorderWidth: 2,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Revenue',
                        data: data.revenueData,
                        borderColor: this.config.currencyColors.primary,
                        backgroundColor: this.config.currencyColors.primary + '20',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        pointBackgroundColor: '#ffffff',
                        pointBorderWidth: 2,
                        yAxisID: 'y1'
                    }
                ];
                
            default: // volume
                return [{
                    label: 'Cases',
                    data: data.volumeData,
                    borderColor: this.config.currencyColors.secondary,
                    backgroundColor: this.config.currencyColors.secondary + '20',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    pointBackgroundColor: '#ffffff',
                    pointBorderWidth: 2,
                    yAxisID: 'y'
                }];
        }
    }

    getScalesByMode() {
        const baseScale = {
            beginAtZero: true,
            grid: {
                color: getComputedStyle(document.documentElement)
                    .getPropertyValue('--border-color').trim()
            }
        };

        if (this.chartMode === 'dual') {
            return {
                x: {
                    grid: { display: false }
                },
                y: {
                    ...baseScale,
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Cases'
                    },
                    ticks: { stepSize: 1 }
                },
                y1: {
                    ...baseScale,
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: `Revenue (${this.currentCurrency})`
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        callback: (value) => this.formatCurrency(value)
                    }
                }
            };
        } else {
            return {
                y: {
                    ...baseScale,
                    title: {
                        display: true,
                        text: this.chartMode === 'revenue' ? `Revenue (${this.currentCurrency})` : 'Cases'
                    },
                    ticks: this.chartMode === 'revenue' ? {
                        callback: (value) => this.formatCurrency(value)
                    } : { stepSize: 1 }
                },
                x: {
                    grid: { display: false }
                }
            };
        }
    }

    setupEnhancedTrendTabs(data30Days, data12Months, data24Months, dataAllTime) {
        const tabs = {
            'trend30DaysBtn': data30Days,
            'trend12MonthsBtn': data12Months,
            'trend24MonthsBtn': data24Months,
            'trendAllTimeBtn': dataAllTime
        };

        Object.entries(tabs).forEach(([btnId, data]) => {
            const btn = document.getElementById(btnId);
            if (btn) {
                btn.addEventListener('click', () => {
                    this.updateEnhancedTrendChart(data);
                    this.setActiveTab(btn);
                });
            }
        });
    }

    updateEnhancedTrendChart(data) {
        if (!this.charts.enhancedTrend || !data) return;

        const datasets = this.getDatasetsByMode(data);
        const scales = this.getScalesByMode();

        this.charts.enhancedTrend.data.labels = data.labels;
        this.charts.enhancedTrend.data.datasets = datasets;
        this.charts.enhancedTrend.options.scales = scales;
        this.charts.enhancedTrend.update('active');
    }

    initRevenueChart() {
        const canvas = document.getElementById('revenueChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        try {
            const statusData = window.statusChartData || [];
            
            if (statusData.length === 0) {
                this.showChartError(canvas.parentNode, 'No revenue data available');
                return;
            }

            // Filter out zero-value statuses for cleaner chart
            const nonZeroData = statusData.filter(item => item.value > 0);
            
            if (nonZeroData.length === 0) {
                this.showChartError(canvas.parentNode, 'No revenue data to display');
                return;
            }

            const labels = nonZeroData.map(item => 
                item.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
            );
            const data = nonZeroData.map(item => item.value);

            this.charts.revenue = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: this.config.defaultColors,
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    ...this.config.chartOptions,
                    cutout: '60%',
                    plugins: {
                        ...this.config.chartOptions.plugins,
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                boxWidth: 12
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: (context) => {
                                    const value = context.parsed;
                                    const formatted = this.formatCurrency(value);
                                    return `${context.label}: ${formatted}`;
                                }
                            }
                        }
                    }
                }
            });

        } catch (error) {
            console.error('Error initializing revenue chart:', error);
            this.showChartError(canvas.parentNode, 'Error loading revenue data');
        }
    }

    setupEventListeners() {
        this.setupDateRangeFilter();
        this.setupSearch();
        this.setupThemeToggle();
        this.setupRefreshButton();
        this.setupChartModeToggle();
        this.setupCurrencySelector();
    }

    setupChartModeToggle() {
        const modeButtons = document.querySelectorAll('.chart-mode-btn');
        modeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const mode = btn.dataset.mode;
                this.setChartMode(mode);
                
                // Update button states
                modeButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });
    }

    setChartMode(mode) {
        this.chartMode = mode;
        
        // Update chart title
        const chartTitle = document.getElementById('chartTitle');
        if (chartTitle) {
            switch (mode) {
                case 'revenue':
                    chartTitle.textContent = 'Revenue by Period';
                    break;
                case 'dual':
                    chartTitle.textContent = 'Cases & Revenue by Period';
                    break;
                default:
                    chartTitle.textContent = 'Case Volume by Period';
            }
        }

        // Recreate chart with new mode
        const canvas = document.getElementById('enhancedTrendChart');
        if (canvas && this.charts.enhancedTrend) {
            // Get current active tab data
            const activeTab = document.querySelector('.tab-control.active');
            if (activeTab) {
                activeTab.click(); // This will trigger chart update with new mode
            }
        }
    }

    setupCurrencySelector() {
        const currencySelector = document.getElementById('currencySelector');
        if (currencySelector) {
            currencySelector.addEventListener('change', (e) => {
                this.convertCurrency(e.target.value);
            });
        }
    }

    async loadExchangeRates() {
        try {
            // In a real implementation, you'd fetch from a live API
            this.exchangeRates = {
                'USD': 1.0,
                'EUR': 0.85,
                'GBP': 0.73,
                'ALL': 100.0,
                'CAD': 1.25
            };
        } catch (error) {
            console.error('Error loading exchange rates:', error);
        }
    }

    async convertCurrency(newCurrency) {
        if (newCurrency === this.currentCurrency) return;

        try {
            // Update all currency displays
            const currencyElements = document.querySelectorAll('[data-metric*="value"]');
            currencyElements.forEach(element => {
                this.updateElementCurrency(element, newCurrency);
            });

            // Update current currency
            this.currentCurrency = newCurrency;

            // Refresh charts with new currency
            this.refreshChartsWithCurrency();

        } catch (error) {
            console.error('Error converting currency:', error);
        }
    }

    updateElementCurrency(element, newCurrency) {
        // This would need to be implemented based on your specific needs
        // For now, we'll add a visual indicator
        const currentText = element.textContent;
        const currencySymbols = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'ALL': 'L',
            'CAD': 'C$'
        };
        
        // Add currency indicator if not already present
        if (!currentText.includes(currencySymbols[newCurrency])) {
            element.setAttribute('data-original-currency', this.currentCurrency);
            element.classList.add('currency-converting');
        }
    }

    refreshChartsWithCurrency() {
        // Update chart scales and tooltips for new currency
        if (this.charts.enhancedTrend) {
            const scales = this.getScalesByMode();
            this.charts.enhancedTrend.options.scales = scales;
            this.charts.enhancedTrend.update('none');
        }

        if (this.charts.revenue) {
            this.charts.revenue.update('none');
        }
    }

    formatCurrency(amount, currency = null) {
        const curr = currency || this.currentCurrency;
        const symbols = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'ALL': 'L',
            'CAD': 'C$'
        };

        const symbol = symbols[curr] || curr + ' ';
        
        if (curr === 'ALL') {
            return `${symbol}${Math.round(amount).toLocaleString()}`;
        } else {
            return `${symbol}${amount.toLocaleString(undefined, {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
            })}`;
        }
    }

    setupDateRangeFilter() {
        const filterElement = document.getElementById('dateRangeFilter');
        if (filterElement) {
            filterElement.addEventListener('change', (e) => {
                this.handleDateRangeChange(e.target.value);
            });
        }
    }

    handleDateRangeChange(days) {
        try {
            const baseUrl = window.location.pathname;
            const url = new URL(baseUrl, window.location.origin);
            url.searchParams.set('range', days);
            url.searchParams.set('enhanced', 'true'); // Maintain enhanced view
            
            this.showLoadingState();
            window.location.href = url.toString();
        } catch (error) {
            console.error('Error changing date range:', error);
            window.location.reload();
        }
    }

    setupSearch() {
        const searchInput = document.getElementById('globalSearch');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch();
                }
            });
        }
    }

    performSearch() {
        const searchInput = document.getElementById('globalSearch');
        const searchTerm = searchInput?.value.trim();
        
        if (searchTerm) {
            const searchUrl = window.searchCasesUrl || '/case/search/';
            window.location.href = `${searchUrl}?q=${encodeURIComponent(searchTerm)}`;
        }
    }

    setupThemeToggle() {
        const themeToggle = document.getElementById('darkModeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    initializeTheme() {
        const savedTheme = localStorage.getItem('dashboard-theme') || 'light';
        this.setTheme(savedTheme);
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('dashboard-theme', theme);
        
        const themeToggle = document.getElementById('darkModeToggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = theme === 'dark' ? 'bi bi-sun' : 'bi bi-moon-stars';
            }
        }

        this.updateChartsTheme();
    }

    updateChartsTheme() {
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.options) {
                const textColor = getComputedStyle(document.documentElement)
                    .getPropertyValue('--text-muted').trim();
                
                Chart.defaults.color = textColor;
                chart.update('none');
            }
        });
    }

    setupRefreshButton() {
        const refreshButton = document.querySelector('[onclick="window.location.reload();"]');
        if (refreshButton) {
            refreshButton.onclick = () => {
                this.showLoadingState();
                window.location.reload();
            };
        }
    }

    setActiveTab(activeBtn) {
        document.querySelectorAll('.tab-control').forEach(btn => {
            btn.classList.remove('active');
            btn.setAttribute('aria-pressed', 'false');
        });
        
        activeBtn.classList.add('active');
        activeBtn.setAttribute('aria-pressed', 'true');
    }

    showChartError(container, message) {
        if (container) {
            container.innerHTML = `
                <div class="empty-state text-center">
                    <i class="bi bi-exclamation-triangle"></i>
                    <p class="empty-state-text">${message}</p>
                </div>
            `;
        }
    }

    showLoadingState() {
        document.querySelectorAll('.metric-card').forEach(card => {
            card.classList.add('loading');
        });
    }

    // Public API
    refresh() {
        this.showLoadingState();
        const url = new URL(window.location);
        url.searchParams.set('enhanced', 'true');
        window.location.href = url.toString();
    }

    switchToBasicView() {
        const url = new URL(window.location);
        url.searchParams.delete('enhanced');
        window.location.href = url.toString();
    }

    exportFinancialData() {
        // Implementation for exporting enhanced financial data
        console.log('Exporting financial data...');
    }

    destroy() {
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
        this.charts = {};
    }
}

// Initialize enhanced dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.enhancedDashboardManager = new EnhancedDashboardManager();
    
    // Maintain backward compatibility
    window.dashboardManager = window.enhancedDashboardManager;
});

// Expose global functions for backward compatibility
window.performSearch = () => {
    if (window.enhancedDashboardManager) {
        window.enhancedDashboardManager.performSearch();
    }
};
