#!/usr/bin/env python
"""
Dashboard Improvement Test Script
Quick verification that the new dashboard implementation works correctly
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

def test_dashboard_imports():
    """Test that all new dashboard components can be imported"""
    print("🔍 Testing dashboard imports...")
    
    try:
        from accounts.services.dashboard_service import (
            DashboardService, DashboardDataService, 
            TrendDataService, ChartDataService
        )
        print("✅ Service layer imports successful")
        
        from accounts.views.dashboard_views import (
            DashboardView, home_function_view, dashboard_api
        )
        print("✅ View imports successful")
        
        from accounts.config.dashboard_config import (
            DashboardConfig, STATUS_COLORS, PRIORITY_COLORS
        )
        print("✅ Configuration imports successful")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_service_initialization():
    """Test that services can be initialized correctly"""
    print("\n🔧 Testing service initialization...")
    
    try:
        from accounts.services.dashboard_service import DashboardService
        
        # Test service initialization
        service = DashboardService(date_range_days=30)
        print("✅ DashboardService initialization successful")
        
        # Test service components
        data_service = service.data_service
        trend_service = service.trend_service  
        chart_service = service.chart_service
        print("✅ Service components accessible")
        
        return True
    except Exception as e:
        print(f"❌ Service initialization error: {e}")
        return False

def test_database_connection():
    """Test that database models are accessible"""
    print("\n💾 Testing database connectivity...")
    
    try:
        from case.models import Case
        from Dentists.models import Dentist
        
        # Test basic queries
        case_count = Case.objects.count()
        dentist_count = Dentist.objects.count()
        
        print(f"✅ Database accessible - {case_count} cases, {dentist_count} dentists")
        return True
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_dashboard_data_generation():
    """Test that dashboard data can be generated"""
    print("\n📊 Testing dashboard data generation...")
    
    try:
        from accounts.services.dashboard_service import DashboardService
        
        service = DashboardService(date_range_days=30)
        dashboard_data = service.get_complete_dashboard_data()
        
        # Check required keys
        required_keys = [
            'total_cases', 'cases_today', 'cases_this_week', 'cases_this_month',
            'overdue_cases', 'ready_to_ship', 'in_progress',
            'trend_30_days_data', 'status_chart_data'
        ]
        
        missing_keys = [key for key in required_keys if key not in dashboard_data]
        
        if missing_keys:
            print(f"❌ Missing keys: {missing_keys}")
            return False
        
        print("✅ Dashboard data generation successful")
        print(f"   - Total cases: {dashboard_data.get('total_cases', 0)}")
        print(f"   - Cases today: {dashboard_data.get('cases_today', 0)}")
        print(f"   - Ready to ship: {dashboard_data.get('ready_to_ship', 0)}")
        
        return True
    except Exception as e:
        print(f"❌ Dashboard data generation error: {e}")
        return False

def test_static_files():
    """Test that static files exist"""
    print("\n🎨 Testing static file structure...")
    
    base_path = os.path.dirname(os.path.abspath(__file__))
    static_files = [
        'static/css/dashboard/variables.css',
        'static/css/dashboard/layout.css', 
        'static/css/dashboard/components.css',
        'static/css/dashboard/notifications.css',
        'static/js/dashboard/dashboard.js'
    ]
    
    missing_files = []
    for file_path in static_files:
        full_path = os.path.join(base_path, file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing static files: {missing_files}")
        return False
    
    print("✅ All static files exist")
    return True

def test_template_files():
    """Test that template files exist"""
    print("\n📄 Testing template files...")
    
    base_path = os.path.dirname(os.path.abspath(__file__))
    template_files = [
        'accounts/templates/home_improved.html'
    ]
    
    missing_files = []
    for file_path in template_files:
        full_path = os.path.join(base_path, file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing template files: {missing_files}")
        return False
    
    print("✅ All template files exist")
    return True

def run_all_tests():
    """Run all tests and provide summary"""
    print("🧪 Dashboard Improvement Test Suite")
    print("=" * 50)
    
    tests = [
        test_dashboard_imports,
        test_service_initialization, 
        test_database_connection,
        test_dashboard_data_generation,
        test_static_files,
        test_template_files
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("🏁 Test Results Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 ALL TESTS PASSED! ({passed}/{total})")
        print("\n✅ Dashboard improvements are ready for deployment!")
        print("\nNext steps:")
        print("1. Test the improved dashboard at /accounts/dashboard/improved/")
        print("2. Compare performance with the original dashboard")
        print("3. Gradually migrate users to the new implementation")
    else:
        print(f"⚠️  {passed}/{total} tests passed")
        print(f"❌ {total - passed} tests failed")
        print("\nPlease resolve the failing tests before deployment.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
