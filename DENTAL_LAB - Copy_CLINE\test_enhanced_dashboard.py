#!/usr/bin/env python
"""
Enhanced Dashboard Test Script
Tests the financial metrics and currency conversion features
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

def test_enhanced_dashboard_imports():
    """Test that enhanced dashboard components can be imported"""
    print("Testing enhanced dashboard imports...")
    
    try:
        from accounts.services.enhanced_dashboard_service import (
            EnhancedDashboardService, EnhancedDashboardDataService,
            EnhancedTrendDataService, FinancialDataService
        )
        print("OK - Enhanced service layer imports successful")
        
        from accounts.views.enhanced_dashboard_views import (
            EnhancedDashboardView, enhanced_home_view, enhanced_dashboard_api,
            financial_metrics_api, currency_conversion_api
        )
        print("OK - Enhanced view imports successful")
        
        return True
    except ImportError as e:
        print(f"FAIL - Enhanced import error: {e}")
        return False

def test_financial_service():
    """Test financial data service functionality"""
    print("\nTesting financial service...")
    
    try:
        from accounts.services.enhanced_dashboard_service import FinancialDataService
        from decimal import Decimal
        
        # Test service initialization
        financial_service = FinancialDataService()
        print("OK - FinancialDataService initialization successful")
        
        # Test currency conversion
        usd_amount = Decimal('100.00')
        eur_amount = financial_service.convert_currency(usd_amount, 'USD', 'EUR')
        print(f"OK - Currency conversion: $100.00 = EUR{eur_amount}")
        
        # Test currency formatting
        formatted_usd = financial_service.format_currency(usd_amount, 'USD')
        formatted_eur = financial_service.format_currency(eur_amount, 'EUR')
        # Use ASCII-safe display for console
        display_eur = formatted_eur.replace('€', 'EUR')
        print(f"OK - Currency formatting: {formatted_usd}, {display_eur}")
        
        return True
    except Exception as e:
        print(f"FAIL - Financial service error: {e}")
        return False

def test_enhanced_data_generation():
    """Test enhanced dashboard data generation with financial metrics"""
    print("\nTesting enhanced data generation...")
    
    try:
        from accounts.services.enhanced_dashboard_service import EnhancedDashboardService
        
        service = EnhancedDashboardService(date_range_days=30)
        dashboard_data = service.get_complete_enhanced_dashboard_data()
        
        # Check required enhanced keys
        enhanced_keys = [
            'total_cases', 'cases_today', 'total_value', 'today_value',
            'month_value', 'avg_case_value', 'total_value_formatted',
            'month_value_formatted', 'status_data', 'currency'
        ]
        
        missing_keys = [key for key in enhanced_keys if key not in dashboard_data]
        
        if missing_keys:
            print(f"FAIL - Missing enhanced keys: {missing_keys}")
            return False
        
        print("OK - Enhanced dashboard data generation successful")
        print(f"   - Total cases: {dashboard_data.get('total_cases', 0)}")
        print(f"   - Total value: {dashboard_data.get('total_value_formatted', '$0.00')}")
        print(f"   - Today's value: {dashboard_data.get('today_value_formatted', '$0.00')}")
        print(f"   - Average case value: {dashboard_data.get('avg_case_value_formatted', '$0.00')}")
        print(f"   - Currency: {dashboard_data.get('currency', 'USD')}")
        
        # Test status data with financial info
        status_data = dashboard_data.get('status_data', [])
        if status_data:
            first_status = status_data[0]
            if 'value_formatted' in first_status:
                print(f"   - First status '{first_status['label']}': {first_status['count']} cases, {first_status['value_formatted']}")
            else:
                print("WARN - Status data missing financial information")
        
        return True
    except Exception as e:
        print(f"FAIL - Enhanced data generation error: {e}")
        return False

def test_enhanced_trend_data():
    """Test enhanced trend data with financial metrics"""
    print("\nTesting enhanced trend data...")
    
    try:
        from accounts.services.enhanced_dashboard_service import EnhancedTrendDataService
        
        trend_service = EnhancedTrendDataService()
        
        # Test 30-day enhanced trend
        trend_data = trend_service.get_enhanced_trend_data("30_days", 30)
        
        required_trend_keys = ['data', 'json', 'total_count', 'total_value', 'total_value_formatted']
        missing_keys = [key for key in required_trend_keys if key not in trend_data]
        
        if missing_keys:
            print(f"FAIL - Missing trend keys: {missing_keys}")
            return False
        
        print("OK - Enhanced trend data generation successful")
        print(f"   - Total cases in period: {trend_data['total_count']}")
        print(f"   - Total value in period: {trend_data['total_value_formatted']}")
        
        # Check individual data points
        data_points = trend_data['data']
        if data_points:
            first_point = data_points[0]
            required_point_keys = ['count', 'value', 'value_formatted', 'avg_case_value']
            if all(key in first_point for key in required_point_keys):
                print("OK - Trend data points contain financial metrics")
            else:
                print("WARN - Trend data points missing some financial metrics")
        
        return True
    except Exception as e:
        print(f"FAIL - Enhanced trend data error: {e}")
        return False

def test_template_files():
    """Test that enhanced template files exist"""
    print("\nTesting enhanced template files...")
    
    base_path = os.path.dirname(os.path.abspath(__file__))
    template_files = [
        'accounts/templates/home_enhanced.html',
        'static/js/dashboard/enhanced-dashboard.js'
    ]
    
    missing_files = []
    for file_path in template_files:
        full_path = os.path.join(base_path, file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"FAIL - Missing enhanced template files: {missing_files}")
        return False
    
    print("OK - All enhanced template files exist")
    return True

def test_case_value_calculation():
    """Test case value calculation from cost estimates and invoices"""
    print("\nTesting case value calculation...")
    
    try:
        from accounts.services.enhanced_dashboard_service import EnhancedDashboardDataService
        from case.models import Case
        from decimal import Decimal
        
        data_service = EnhancedDashboardDataService()
        
        # Test with a sample of cases
        sample_cases = Case.objects.all()[:10]  # Get first 10 cases
        
        if sample_cases.exists():
            total_value = data_service._calculate_case_value(sample_cases)
            print(f"OK - Case value calculation successful")
            print(f"   - Sample cases count: {sample_cases.count()}")
            print(f"   - Calculated total value: ${total_value}")
            
            # Test average calculation
            avg_value = total_value / sample_cases.count() if sample_cases.count() > 0 else Decimal('0.00')
            print(f"   - Average case value: ${avg_value}")
            
            return True
        else:
            print("WARN - No cases found to test value calculation")
            return True  # Not a failure if no data exists
            
    except Exception as e:
        print(f"FAIL - Case value calculation error: {e}")
        return False

def test_currency_conversion_api():
    """Test currency conversion functionality"""
    print("\nTesting currency conversion...")
    
    try:
        from accounts.services.enhanced_dashboard_service import FinancialDataService
        from decimal import Decimal
        
        financial_service = FinancialDataService()
        
        # Test various currency conversions
        test_conversions = [
            (100, 'USD', 'EUR'),
            (1000, 'USD', 'ALL'),
            (50, 'EUR', 'GBP'),
            (200, 'GBP', 'USD')
        ]
        
        for amount, from_curr, to_curr in test_conversions:
            converted = financial_service.convert_currency(Decimal(str(amount)), from_curr, to_curr)
            formatted = financial_service.format_currency(converted, to_curr)
            # Use ASCII-safe display for console output
            display_formatted = formatted.replace('€', 'EUR').replace('£', 'GBP').replace('L', 'ALL')
            print(f"   - {amount} {from_curr} = {display_formatted}")
        
        print("OK - Currency conversion tests successful")
        return True
        
    except Exception as e:
        print(f"FAIL - Currency conversion error: {e}")
        return False

def run_enhanced_tests():
    """Run all enhanced dashboard tests"""
    print("Enhanced Dashboard Test Suite")
    print("=" * 50)
    
    tests = [
        test_enhanced_dashboard_imports,
        test_financial_service,
        test_enhanced_data_generation,
        test_enhanced_trend_data,
        test_template_files,
        test_case_value_calculation,
        test_currency_conversion_api
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"FAIL - Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("Enhanced Dashboard Test Results")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"SUCCESS: ALL ENHANCED TESTS PASSED! ({passed}/{total})")
        print("\nEnhanced dashboard with financial metrics is ready!")
        print("\nAccess URLs:")
        print("- Basic Dashboard: /accounts/dashboard/improved/")
        print("- Enhanced Dashboard: /accounts/dashboard/enhanced/")
        print("- Financial Dashboard: /accounts/dashboard/financial/")
        print("- Enhanced via parameter: /?enhanced=true")
        print("\nFeatures available:")
        print("- Volume and revenue tracking")
        print("- Multi-currency support")
        print("- Dual-axis charts")
        print("- Financial status breakdowns")
        print("- Currency conversion")
    else:
        print(f"PARTIAL SUCCESS: {passed}/{total} tests passed")
        print(f"FAILED: {total - passed} tests failed")
        print("\nPlease resolve failing tests before using enhanced features.")
    
    return passed == total

if __name__ == "__main__":
    success = run_enhanced_tests()
    sys.exit(0 if success else 1)
