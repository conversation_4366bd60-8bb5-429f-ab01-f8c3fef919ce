#!/usr/bin/env python
"""
Professional Dashboard Test Script
Tests the professional financial analytics and executive dashboard features
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

def test_professional_dashboard_imports():
    """Test that professional dashboard components can be imported"""
    print("Testing professional dashboard imports...")
    
    try:
        from accounts.services.professional_dashboard_service import (
            ProfessionalDashboardService, ProfessionalFinancialService
        )
        print("OK - Professional service layer imports successful")
        
        from accounts.views.professional_dashboard_views import (
            ProfessionalDashboardView, professional_home_view, professional_dashboard_api,
            financial_summary_api, operational_metrics_api
        )
        print("OK - Professional view imports successful")
        
        return True
    except ImportError as e:
        print(f"FAIL - Professional import error: {e}")
        return False

def test_professional_financial_service():
    """Test professional financial service functionality"""
    print("\nTesting professional financial service...")
    
    try:
        from accounts.services.professional_dashboard_service import ProfessionalFinancialService
        from decimal import Decimal
        
        # Test service initialization
        financial_service = ProfessionalFinancialService()
        print("OK - ProfessionalFinancialService initialization successful")
        
        # Test financial metrics calculation
        financial_metrics = financial_service.calculate_financial_metrics()
        
        required_keys = [
            'total_invoiced', 'total_income', 'total_receivable', 
            'collection_rate', 'avg_case_value', 'days_sales_outstanding'
        ]
        
        missing_keys = [key for key in required_keys if key not in financial_metrics]
        
        if missing_keys:
            print(f"FAIL - Missing financial metric keys: {missing_keys}")
            return False
        
        print("OK - Financial metrics calculation successful")
        print(f"   - Total invoiced: ${financial_metrics['total_invoiced']}")
        print(f"   - Total income: ${financial_metrics['total_income']}")
        print(f"   - Total receivable: ${financial_metrics['total_receivable']}")
        print(f"   - Collection rate: {financial_metrics['collection_rate']}%")
        print(f"   - Average case value: ${financial_metrics['avg_case_value']}")
        print(f"   - Days sales outstanding: {financial_metrics['days_sales_outstanding']}")
        
        return True
    except Exception as e:
        print(f"FAIL - Professional financial service error: {e}")
        return False

def test_executive_summary():
    """Test executive summary generation"""
    print("\nTesting executive summary...")
    
    try:
        from accounts.services.professional_dashboard_service import ProfessionalDashboardService
        
        service = ProfessionalDashboardService(date_range_days=30)
        executive_summary = service.get_executive_summary()
        
        # Check required executive summary keys
        executive_keys = [
            'total_cases', 'active_cases', 'mtd_invoiced', 'mtd_income', 
            'mtd_receivable', 'collection_rate', 'avg_case_value',
            'mtd_invoiced_formatted', 'mtd_income_formatted', 'mtd_receivable_formatted'
        ]
        
        missing_keys = [key for key in executive_keys if key not in executive_summary]
        
        if missing_keys:
            print(f"FAIL - Missing executive summary keys: {missing_keys}")
            return False
        
        print("OK - Executive summary generation successful")
        print(f"   - Total cases: {executive_summary.get('total_cases', 0)}")
        print(f"   - Active cases: {executive_summary.get('active_cases', 0)}")
        print(f"   - MTD invoiced: {executive_summary.get('mtd_invoiced_formatted', '$0.00')}")
        print(f"   - MTD income: {executive_summary.get('mtd_income_formatted', '$0.00')}")
        print(f"   - MTD receivable: {executive_summary.get('mtd_receivable_formatted', '$0.00')}")
        print(f"   - Collection rate: {executive_summary.get('collection_rate', 0)}%")
        print(f"   - Avg case value: {executive_summary.get('avg_case_value_formatted', '$0.00')}")
        
        return True
    except Exception as e:
        print(f"FAIL - Executive summary error: {e}")
        return False

def test_operational_metrics():
    """Test operational metrics generation"""
    print("\nTesting operational metrics...")
    
    try:
        from accounts.services.professional_dashboard_service import ProfessionalDashboardService
        
        service = ProfessionalDashboardService(date_range_days=30)
        operational_metrics = service.get_operational_metrics()
        
        # Check required operational metrics keys
        operational_keys = [
            'status_analysis', 'overdue_cases', 'overdue_value', 
            'high_value_cases', 'ready_to_ship', 'in_production'
        ]
        
        missing_keys = [key for key in operational_keys if key not in operational_metrics]
        
        if missing_keys:
            print(f"FAIL - Missing operational metrics keys: {missing_keys}")
            return False
        
        print("OK - Operational metrics generation successful")
        print(f"   - Overdue cases: {operational_metrics.get('overdue_cases', 0)}")
        print(f"   - Overdue value: {operational_metrics.get('overdue_value_formatted', '$0.00')}")
        print(f"   - High-value cases: {operational_metrics.get('high_value_cases', 0)}")
        print(f"   - Ready to ship: {operational_metrics.get('ready_to_ship', 0)}")
        print(f"   - In production: {operational_metrics.get('in_production', 0)}")
        
        # Check status analysis
        status_analysis = operational_metrics.get('status_analysis', [])
        if status_analysis:
            first_status = status_analysis[0]
            print(f"   - Top status: {first_status['label']} ({first_status['case_count']} cases, {first_status['invoiced_formatted']})")
        
        return True
    except Exception as e:
        print(f"FAIL - Operational metrics error: {e}")
        return False

def test_financial_trends():
    """Test financial trends generation"""
    print("\nTesting financial trends...")
    
    try:
        from accounts.services.professional_dashboard_service import ProfessionalDashboardService
        
        service = ProfessionalDashboardService(date_range_days=30)
        
        # Test daily trends
        daily_trends = service.get_financial_trends("30_days")
        
        required_trend_keys = ['data', 'json', 'period_summary']
        missing_keys = [key for key in required_trend_keys if key not in daily_trends]
        
        if missing_keys:
            print(f"FAIL - Missing trend keys: {missing_keys}")
            return False
        
        print("OK - Financial trends generation successful")
        
        # Check trend data points
        trend_data = daily_trends['data']
        if trend_data:
            first_point = trend_data[0]
            required_point_keys = ['cases', 'invoiced', 'income', 'receivable']
            if all(key in first_point for key in required_point_keys):
                print("OK - Trend data points contain all financial metrics")
            else:
                print("WARN - Trend data points missing some financial metrics")
        
        # Check period summary
        period_summary = daily_trends['period_summary']
        print(f"   - Period total invoiced: {period_summary.get('total_invoiced_formatted', '$0.00')}")
        print(f"   - Period total income: {period_summary.get('total_income_formatted', '$0.00')}")
        print(f"   - Period total receivable: {period_summary.get('total_receivable_formatted', '$0.00')}")
        
        return True
    except Exception as e:
        print(f"FAIL - Financial trends error: {e}")
        return False

def test_complete_dashboard_data():
    """Test complete professional dashboard data generation"""
    print("\nTesting complete dashboard data...")
    
    try:
        from accounts.services.professional_dashboard_service import ProfessionalDashboardService
        
        service = ProfessionalDashboardService(date_range_days=30)
        dashboard_data = service.get_complete_dashboard_data()
        
        # Check that all sections are included
        required_sections = [
            'total_cases', 'active_cases', 'mtd_invoiced_formatted',
            'status_analysis', 'trends_30_days_data', 'recent_cases',
            'dashboard_type'
        ]
        
        missing_sections = [key for key in required_sections if key not in dashboard_data]
        
        if missing_sections:
            print(f"FAIL - Missing dashboard sections: {missing_sections}")
            return False
        
        print("OK - Complete dashboard data generation successful")
        print(f"   - Dashboard type: {dashboard_data.get('dashboard_type', 'unknown')}")
        print(f"   - Total sections: {len(dashboard_data)} data sections")
        print(f"   - Currency: {dashboard_data.get('currency', 'USD')}")
        print(f"   - Selected range: {dashboard_data.get('selected_range', '30')} days")
        
        return True
    except Exception as e:
        print(f"FAIL - Complete dashboard data error: {e}")
        return False

def test_template_files():
    """Test that professional template files exist"""
    print("\nTesting professional template files...")
    
    base_path = os.path.dirname(os.path.abspath(__file__))
    template_files = [
        'accounts/templates/professional_dashboard.html'
    ]
    
    missing_files = []
    for file_path in template_files:
        full_path = os.path.join(base_path, file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"FAIL - Missing professional template files: {missing_files}")
        return False
    
    print("OK - All professional template files exist")
    return True

def run_professional_tests():
    """Run all professional dashboard tests"""
    print("Professional Dashboard Test Suite")
    print("=" * 50)
    
    tests = [
        test_professional_dashboard_imports,
        test_professional_financial_service,
        test_executive_summary,
        test_operational_metrics,
        test_financial_trends,
        test_complete_dashboard_data,
        test_template_files
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"FAIL - Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("Professional Dashboard Test Results")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"SUCCESS: ALL PROFESSIONAL TESTS PASSED! ({passed}/{total})")
        print("\nProfessional executive dashboard is ready!")
        print("\nAccess URLs:")
        print("- Basic Dashboard: /accounts/dashboard/improved/")
        print("- Enhanced Dashboard: /accounts/dashboard/enhanced/")
        print("- Professional Dashboard: /accounts/dashboard/professional/")
        print("- Executive Dashboard: /accounts/dashboard/executive/")
        print("- Professional via parameter: /?professional=true")
        print("\nProfessional Features:")
        print("- Executive-level financial analytics")
        print("- Invoiced, Receivable, and Income tracking")
        print("- Days Sales Outstanding (DSO)")
        print("- Collection rate analysis")
        print("- Status-based financial breakdowns")
        print("- Professional design and layout")
        print("- Executive summary cards")
        print("- Operational alerts and KPIs")
    else:
        print(f"PARTIAL SUCCESS: {passed}/{total} tests passed")
        print(f"FAILED: {total - passed} tests failed")
        print("\nPlease resolve failing tests before using professional features.")
    
    return passed == total

if __name__ == "__main__":
    success = run_professional_tests()
    sys.exit(0 if success else 1)
